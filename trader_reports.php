<?php
// Database configuration
$host = 'localhost';
$dbname = 'ccis_system';
$username = 'root';
$password = '';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch(PDOException $e) {
    die("Connection failed: " . $e->getMessage());
}

// Get selected trader
$selected_trader = isset($_GET['trader_id']) ? (int)$_GET['trader_id'] : null;

// Get all traders for dropdown
$traders_query = "SELECT id, name, code FROM traders WHERE deleted_at IS NULL ORDER BY name";
$traders = $pdo->query($traders_query)->fetchAll(PDO::FETCH_ASSOC);

// Initialize data arrays
$financial_data = [];
$container_data = [];
$fines_data = [];
$fees_data = [];
$summary_data = [];
$monthly_data = [];
$annual_data = [];
$cash_flow_data = [];

if ($selected_trader) {
    // Get financial documents data
    $financial_query = "
        SELECT 
            fd.document_date,
            CASE fd.document_type
                WHEN 'receipt' THEN 'إيصال استلام'
                WHEN 'payment' THEN 'إيصال دفع'
                WHEN 'expense' THEN 'مصروف'
                WHEN 'transfer' THEN 'تحويل'
                WHEN 'capital' THEN 'رأس مال'
            END AS document_type_ar,
            fd.amount,
            fd.notes,
            CASE 
                WHEN fd.document_type IN ('receipt', 'capital') THEN fd.amount 
                ELSE 0 
            END AS debit,
            CASE 
                WHEN fd.document_type IN ('payment', 'expense') THEN fd.amount 
                ELSE 0 
            END AS credit
        FROM financial_documents fd
        WHERE fd.trader_id = ? AND fd.deleted_at IS NULL
        ORDER BY fd.document_date DESC, fd.id DESC
        LIMIT 50
    ";
    $stmt = $pdo->prepare($financial_query);
    $stmt->execute([$selected_trader]);
    $financial_data = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Calculate running balance
    $balance = 0;
    foreach ($financial_data as &$row) {
        $balance += ($row['debit'] - $row['credit']);
        $row['balance'] = $balance;
    }
    $financial_data = array_reverse($financial_data); // Show chronological order

    // Get monthly income/expense data
    $monthly_query = "
        SELECT 
            YEAR(document_date) as year,
            MONTH(document_date) as month,
            MONTHNAME(document_date) as month_name,
            SUM(CASE WHEN document_type = 'receipt' THEN amount ELSE 0 END) as total_receipts,
            SUM(CASE WHEN document_type = 'payment' THEN amount ELSE 0 END) as total_payments,
            SUM(CASE WHEN document_type = 'expense' THEN amount ELSE 0 END) as total_expenses,
            (SUM(CASE WHEN document_type = 'receipt' THEN amount ELSE 0 END) - 
             SUM(CASE WHEN document_type IN ('payment', 'expense') THEN amount ELSE 0 END)) as net_balance
        FROM financial_documents 
        WHERE trader_id = ? AND deleted_at IS NULL
        GROUP BY YEAR(document_date), MONTH(document_date)
        ORDER BY year DESC, month DESC
        LIMIT 12
    ";
    $stmt = $pdo->prepare($monthly_query);
    $stmt->execute([$selected_trader]);
    $monthly_data = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Get container data
    $container_query = "
        SELECT 
            c.container_number,
            c.entry_date,
            c.exit_date,
            CASE c.status
                WHEN 'pending' THEN 'في الانتظار'
                WHEN 'in_progress' THEN 'قيد التنفيذ'
                WHEN 'completed' THEN 'مكتملة'
                WHEN 'cancelled' THEN 'ملغية'
            END AS status_ar,
            c.continer_content,
            c.contyner_size,
            c.purchase_price,
            c.selling_price,
            (c.selling_price - c.purchase_price) as profit_loss,
            DATEDIFF(c.exit_date, c.entry_date) as storage_days,
            COALESCE(f.total_fines, 0) as total_fines,
            COALESCE(of.total_fees, 0) as total_fees,
            ((c.selling_price - c.purchase_price) - COALESCE(f.total_fines, 0) - COALESCE(of.total_fees, 0)) as net_profit
        FROM containers c
        LEFT JOIN (
            SELECT container_id, SUM(amount) as total_fines 
            FROM fines 
            WHERE deleted_at IS NULL 
            GROUP BY container_id
        ) f ON c.id = f.container_id
        LEFT JOIN (
            SELECT container_id, SUM(amount) as total_fees 
            FROM other_fees 
            WHERE deleted_at IS NULL 
            GROUP BY container_id
        ) of ON c.id = of.container_id
        WHERE c.trader_id = ? AND c.deleted_at IS NULL
        ORDER BY c.entry_date DESC
    ";
    $stmt = $pdo->prepare($container_query);
    $stmt->execute([$selected_trader]);
    $container_data = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Get fines data
    $fines_query = "
        SELECT 
            f.fine_date,
            c.container_number,
            CASE f.fine_type
                WHEN 'delay' THEN 'غرامة تأخير'
                WHEN 'damages' THEN 'غرامة أضرار'
                ELSE 'غرامة أخرى'
            END as fine_type_ar,
            f.amount,
            f.notes
        FROM fines f
        JOIN containers c ON f.container_id = c.id
        WHERE f.trader_id = ? AND f.deleted_at IS NULL
        ORDER BY f.fine_date DESC
    ";
    $stmt = $pdo->prepare($fines_query);
    $stmt->execute([$selected_trader]);
    $fines_data = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Get fees data
    $fees_query = "
        SELECT 
            of.fee_date,
            c.container_number,
            CASE of.types_fee
                WHEN 'returns and fees' THEN 'رسوم إرجاع'
                WHEN 'location fee' THEN 'رسوم موقع'
                WHEN 'handling fee' THEN 'رسوم مناولة'
                ELSE 'رسوم أخرى'
            END as fee_type_ar,
            of.amount,
            of.notes
        FROM other_fees of
        JOIN containers c ON of.container_id = c.id
        WHERE of.trader_id = ? AND of.deleted_at IS NULL
        ORDER BY of.fee_date DESC
    ";
    $stmt = $pdo->prepare($fees_query);
    $stmt->execute([$selected_trader]);
    $fees_data = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Get summary data
    $summary_query = "
        SELECT 
            t.name as trader_name,
            t.code as trader_code,
            (SELECT COUNT(*) FROM containers WHERE trader_id = t.id AND deleted_at IS NULL) as total_containers,
            (SELECT COUNT(*) FROM containers WHERE trader_id = t.id AND status = 'completed' AND deleted_at IS NULL) as completed_containers,
            (SELECT COALESCE(SUM(amount), 0) FROM financial_documents WHERE trader_id = t.id AND document_type = 'receipt' AND deleted_at IS NULL) as total_receipts,
            (SELECT COALESCE(SUM(amount), 0) FROM financial_documents WHERE trader_id = t.id AND document_type = 'payment' AND deleted_at IS NULL) as total_payments,
            (SELECT COALESCE(SUM(amount), 0) FROM fines WHERE trader_id = t.id AND deleted_at IS NULL) as total_fines,
            (SELECT COALESCE(SUM(amount), 0) FROM other_fees WHERE trader_id = t.id AND deleted_at IS NULL) as total_fees,
            ((SELECT COALESCE(SUM(amount), 0) FROM financial_documents WHERE trader_id = t.id AND document_type IN ('receipt', 'capital') AND deleted_at IS NULL) - 
             (SELECT COALESCE(SUM(amount), 0) FROM financial_documents WHERE trader_id = t.id AND document_type IN ('payment', 'expense') AND deleted_at IS NULL)) as current_balance
        FROM traders t
        WHERE t.id = ? AND t.deleted_at IS NULL
    ";
    $stmt = $pdo->prepare($summary_query);
    $stmt->execute([$selected_trader]);
    $summary_data = $stmt->fetch(PDO::FETCH_ASSOC);

    // Get annual performance data
    $annual_query = "
        SELECT 
            YEAR(document_date) as year,
            COUNT(CASE WHEN document_type = 'receipt' THEN 1 END) as receipt_count,
            SUM(CASE WHEN document_type = 'receipt' THEN amount ELSE 0 END) as total_receipts,
            COUNT(CASE WHEN document_type = 'payment' THEN 1 END) as payment_count,
            SUM(CASE WHEN document_type = 'payment' THEN amount ELSE 0 END) as total_payments,
            SUM(CASE WHEN document_type = 'expense' THEN amount ELSE 0 END) as total_expenses
        FROM financial_documents fd
        WHERE fd.trader_id = ? AND fd.deleted_at IS NULL
        GROUP BY YEAR(document_date)
        ORDER BY year DESC
        LIMIT 5
    ";
    $stmt = $pdo->prepare($annual_query);
    $stmt->execute([$selected_trader]);
    $annual_data = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Get cash flow data
    $cash_flow_query = "
        SELECT 
            DATE_FORMAT(document_date, '%Y-%m') as month,
            SUM(CASE WHEN document_type IN ('receipt', 'capital') THEN amount ELSE 0 END) as cash_inflow,
            SUM(CASE WHEN document_type IN ('payment', 'expense') THEN amount ELSE 0 END) as cash_outflow,
            (SUM(CASE WHEN document_type IN ('receipt', 'capital') THEN amount ELSE 0 END) - 
             SUM(CASE WHEN document_type IN ('payment', 'expense') THEN amount ELSE 0 END)) as net_cash_flow
        FROM financial_documents
        WHERE trader_id = ? AND deleted_at IS NULL
        GROUP BY DATE_FORMAT(document_date, '%Y-%m')
        ORDER BY month DESC
        LIMIT 12
    ";
    $stmt = $pdo->prepare($cash_flow_query);
    $stmt->execute([$selected_trader]);
    $cash_flow_data = $stmt->fetchAll(PDO::FETCH_ASSOC);
}

// Helper function to format currency
function formatCurrency($amount) {
    return number_format($amount, 0, '.', ',') . ' ريال';
}

// Helper function to format date
function formatDate($date) {
    return date('Y-m-d', strtotime($date));
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقارير التجار - نظام CCIS</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .main-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin: 20px;
            padding: 30px;
        }
        
        .header-section {
            background: linear-gradient(45deg, #2c3e50, #3498db);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .report-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            border-left: 5px solid #3498db;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .report-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
        }
        
        .report-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #ecf0f1;
        }
        
        .report-icon {
            font-size: 2.5rem;
            margin-left: 15px;
            color: #3498db;
        }
        
        .report-title {
            font-size: 1.5rem;
            font-weight: bold;
            color: #2c3e50;
            margin: 0;
        }
        
        .nav-pills .nav-link {
            border-radius: 25px;
            margin: 0 5px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .nav-pills .nav-link.active {
            background: linear-gradient(45deg, #3498db, #2980b9);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
        }
        
        .chart-container {
            background: white;
            padding: 20px;
            border-radius: 15px;
            margin: 20px 0;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .trader-selector {
            background: white;
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .table-responsive {
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .table th {
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
            border: none;
            font-weight: 600;
        }
        
        .table td {
            border-color: #ecf0f1;
            vertical-align: middle;
        }
        
        .summary-card {
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            margin-bottom: 20px;
        }
        
        .summary-card h4 {
            margin: 0;
            font-size: 1.8rem;
        }
        
        .summary-card small {
            opacity: 0.9;
        }
        
        .btn-custom {
            background: linear-gradient(45deg, #3498db, #2980b9);
            border: none;
            border-radius: 25px;
            padding: 12px 30px;
            color: white;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-custom:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(52, 152, 219, 0.4);
            color: white;
        }
        
        .alert-custom {
            border-radius: 15px;
            border: none;
            padding: 20px;
            margin: 15px 0;
        }
        
        @media (max-width: 768px) {
            .main-container {
                margin: 10px;
                padding: 15px;
            }
            
            .report-card {
                padding: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- Header Section -->
        <div class="header-section">
            <h1><i class="fas fa-chart-line"></i> نظام تقارير التجار الشامل</h1>
            <p class="mb-0">تقارير احترافية ومتقدمة لإدارة حسابات التجار في نظام CCIS</p>
            <small>آخر تحديث: <?php echo date('Y-m-d H:i:s'); ?></small>
        </div>

        <!-- Trader Selection -->
        <div class="trader-selector">
            <form method="GET" action="">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <label for="trader_id" class="form-label fw-bold">اختر التاجر:</label>
                        <select class="form-select" id="trader_id" name="trader_id" onchange="this.form.submit()">
                            <option value="">-- اختر التاجر --</option>
                            <?php foreach ($traders as $trader): ?>
                                <option value="<?php echo $trader['id']; ?>"
                                        <?php echo ($selected_trader == $trader['id']) ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($trader['name']) . ' (' . htmlspecialchars($trader['code']) . ')'; ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-md-6 text-end">
                        <?php if ($selected_trader): ?>
                            <button type="button" class="btn btn-outline-secondary" onclick="window.print()">
                                <i class="fas fa-print"></i> طباعة
                            </button>
                            <button type="button" class="btn btn-outline-success" onclick="exportToExcel()">
                                <i class="fas fa-file-excel"></i> تصدير Excel
                            </button>
                        <?php endif; ?>
                    </div>
                </div>
            </form>
        </div>

        <?php if ($selected_trader && $summary_data): ?>
        <!-- Summary Cards -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="summary-card">
                    <h4><?php echo $summary_data['total_containers']; ?></h4>
                    <small>إجمالي الحاويات</small>
                </div>
            </div>
            <div class="col-md-3">
                <div class="summary-card" style="background: linear-gradient(45deg, #27ae60, #2ecc71);">
                    <h4><?php echo formatCurrency($summary_data['total_receipts']); ?></h4>
                    <small>إجمالي الإيصالات</small>
                </div>
            </div>
            <div class="col-md-3">
                <div class="summary-card" style="background: linear-gradient(45deg, #e74c3c, #c0392b);">
                    <h4><?php echo formatCurrency($summary_data['total_fines']); ?></h4>
                    <small>إجمالي الغرامات</small>
                </div>
            </div>
            <div class="col-md-3">
                <div class="summary-card" style="background: linear-gradient(45deg, #f39c12, #e67e22);">
                    <h4><?php echo formatCurrency($summary_data['current_balance']); ?></h4>
                    <small>الرصيد الحالي</small>
                </div>
            </div>
        </div>

        <!-- Navigation Tabs -->
        <ul class="nav nav-pills justify-content-center mb-4" id="reportTabs">
            <li class="nav-item">
                <a class="nav-link active" data-bs-toggle="pill" href="#financial-reports">
                    <i class="fas fa-money-bill-wave"></i> التقارير المالية
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" data-bs-toggle="pill" href="#container-reports">
                    <i class="fas fa-shipping-fast"></i> تقارير الحاويات
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" data-bs-toggle="pill" href="#fees-reports">
                    <i class="fas fa-receipt"></i> الغرامات والرسوم
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" data-bs-toggle="pill" href="#analytics-reports">
                    <i class="fas fa-chart-bar"></i> التحليلات المتقدمة
                </a>
            </li>
        </ul>

        <!-- Tab Content -->
        <div class="tab-content">
            <!-- Financial Reports Tab -->
            <div class="tab-pane fade show active" id="financial-reports">
                <!-- Statement of Account Report -->
                <div class="report-card">
                    <div class="report-header">
                        <i class="fas fa-file-invoice-dollar report-icon"></i>
                        <h3 class="report-title">كشف حساب التاجر</h3>
                    </div>
                    <p class="text-muted">عرض جميع المعاملات المالية للتاجر مع الأرصدة الجارية</p>

                    <?php if (!empty($financial_data)): ?>
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>تاريخ المعاملة</th>
                                    <th>نوع المعاملة</th>
                                    <th>المبلغ</th>
                                    <th>مدين</th>
                                    <th>دائن</th>
                                    <th>الرصيد</th>
                                    <th>الملاحظات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($financial_data as $row): ?>
                                <tr>
                                    <td><?php echo formatDate($row['document_date']); ?></td>
                                    <td><?php echo htmlspecialchars($row['document_type_ar']); ?></td>
                                    <td><?php echo formatCurrency($row['amount']); ?></td>
                                    <td class="text-success"><?php echo $row['debit'] > 0 ? formatCurrency($row['debit']) : '-'; ?></td>
                                    <td class="text-danger"><?php echo $row['credit'] > 0 ? formatCurrency($row['credit']) : '-'; ?></td>
                                    <td class="fw-bold <?php echo $row['balance'] >= 0 ? 'text-success' : 'text-danger'; ?>">
                                        <?php echo formatCurrency($row['balance']); ?>
                                    </td>
                                    <td><?php echo htmlspecialchars($row['notes'] ?: '-'); ?></td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    <?php else: ?>
                    <div class="alert alert-info alert-custom">
                        <i class="fas fa-info-circle"></i>
                        لا توجد معاملات مالية لهذا التاجر
                    </div>
                    <?php endif; ?>
                </div>

                <!-- Monthly Income/Expense Report -->
                <div class="report-card">
                    <div class="report-header">
                        <i class="fas fa-calendar-alt report-icon"></i>
                        <h3 class="report-title">تقرير الإيرادات والمصروفات الشهري</h3>
                    </div>
                    <p class="text-muted">تحليل مفصل للإيرادات والمصروفات مقسمة حسب الأشهر</p>

                    <?php if (!empty($monthly_data)): ?>
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>السنة</th>
                                    <th>الشهر</th>
                                    <th>إجمالي الإيصالات</th>
                                    <th>إجمالي المدفوعات</th>
                                    <th>إجمالي المصروفات</th>
                                    <th>صافي الرصيد</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($monthly_data as $row): ?>
                                <tr>
                                    <td><?php echo $row['year']; ?></td>
                                    <td><?php echo $row['month_name']; ?></td>
                                    <td class="text-success"><?php echo formatCurrency($row['total_receipts']); ?></td>
                                    <td class="text-warning"><?php echo formatCurrency($row['total_payments']); ?></td>
                                    <td class="text-danger"><?php echo formatCurrency($row['total_expenses']); ?></td>
                                    <td class="fw-bold <?php echo $row['net_balance'] >= 0 ? 'text-success' : 'text-danger'; ?>">
                                        <?php echo formatCurrency($row['net_balance']); ?>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>

                    <div class="chart-container">
                        <canvas id="monthlyChart" width="400" height="200"></canvas>
                    </div>
                    <?php else: ?>
                    <div class="alert alert-info alert-custom">
                        <i class="fas fa-info-circle"></i>
                        لا توجد بيانات شهرية لهذا التاجر
                    </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Container Reports Tab -->
            <div class="tab-pane fade" id="container-reports">
                <!-- Container Status Report -->
                <div class="report-card">
                    <div class="report-header">
                        <i class="fas fa-boxes report-icon"></i>
                        <h3 class="report-title">تقرير حالة الحاويات</h3>
                    </div>
                    <p class="text-muted">عرض شامل لجميع الحاويات مع حالتها الحالية وتفاصيل الربحية</p>

                    <?php if (!empty($container_data)): ?>
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>رقم الحاوية</th>
                                    <th>تاريخ الدخول</th>
                                    <th>تاريخ الخروج</th>
                                    <th>الحالة</th>
                                    <th>المحتوى</th>
                                    <th>الحجم</th>
                                    <th>سعر الشراء</th>
                                    <th>سعر البيع</th>
                                    <th>صافي الربح</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($container_data as $row): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($row['container_number']); ?></td>
                                    <td><?php echo formatDate($row['entry_date']); ?></td>
                                    <td><?php echo formatDate($row['exit_date']); ?></td>
                                    <td>
                                        <span class="badge bg-<?php
                                            echo $row['status_ar'] == 'مكتملة' ? 'success' :
                                                ($row['status_ar'] == 'قيد التنفيذ' ? 'warning' : 'secondary');
                                        ?>">
                                            <?php echo htmlspecialchars($row['status_ar']); ?>
                                        </span>
                                    </td>
                                    <td><?php echo htmlspecialchars($row['continer_content']); ?></td>
                                    <td><?php echo htmlspecialchars($row['contyner_size']); ?></td>
                                    <td><?php echo formatCurrency($row['purchase_price']); ?></td>
                                    <td><?php echo formatCurrency($row['selling_price']); ?></td>
                                    <td class="fw-bold <?php echo $row['net_profit'] >= 0 ? 'text-success' : 'text-danger'; ?>">
                                        <?php echo formatCurrency($row['net_profit']); ?>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>

                    <div class="chart-container">
                        <canvas id="profitabilityChart" width="400" height="200"></canvas>
                    </div>
                    <?php else: ?>
                    <div class="alert alert-info alert-custom">
                        <i class="fas fa-info-circle"></i>
                        لا توجد حاويات لهذا التاجر
                    </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Fees and Fines Reports Tab -->
            <div class="tab-pane fade" id="fees-reports">
                <!-- Detailed Fines Report -->
                <div class="report-card">
                    <div class="report-header">
                        <i class="fas fa-gavel report-icon text-danger"></i>
                        <h3 class="report-title">تقرير الغرامات التفصيلي</h3>
                    </div>
                    <p class="text-muted">عرض جميع الغرامات المطبقة مع تفاصيلها وأسبابها</p>

                    <?php if (!empty($fines_data)): ?>
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>تاريخ الغرامة</th>
                                    <th>رقم الحاوية</th>
                                    <th>نوع الغرامة</th>
                                    <th>المبلغ</th>
                                    <th>الملاحظات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($fines_data as $row): ?>
                                <tr>
                                    <td><?php echo formatDate($row['fine_date']); ?></td>
                                    <td><?php echo htmlspecialchars($row['container_number']); ?></td>
                                    <td><?php echo htmlspecialchars($row['fine_type_ar']); ?></td>
                                    <td class="text-danger fw-bold"><?php echo formatCurrency($row['amount']); ?></td>
                                    <td><?php echo htmlspecialchars($row['notes'] ?: '-'); ?></td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    <?php else: ?>
                    <div class="alert alert-success alert-custom">
                        <i class="fas fa-check-circle"></i>
                        لا توجد غرامات مطبقة على هذا التاجر
                    </div>
                    <?php endif; ?>
                </div>

                <!-- Additional Fees Report -->
                <div class="report-card">
                    <div class="report-header">
                        <i class="fas fa-money-check-alt report-icon text-info"></i>
                        <h3 class="report-title">تقرير الرسوم الإضافية</h3>
                    </div>
                    <p class="text-muted">عرض جميع الرسوم الإضافية المطبقة على الحاويات</p>

                    <?php if (!empty($fees_data)): ?>
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>تاريخ الرسوم</th>
                                    <th>رقم الحاوية</th>
                                    <th>نوع الرسوم</th>
                                    <th>المبلغ</th>
                                    <th>الملاحظات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($fees_data as $row): ?>
                                <tr>
                                    <td><?php echo formatDate($row['fee_date']); ?></td>
                                    <td><?php echo htmlspecialchars($row['container_number']); ?></td>
                                    <td><?php echo htmlspecialchars($row['fee_type_ar']); ?></td>
                                    <td class="text-warning fw-bold"><?php echo formatCurrency($row['amount']); ?></td>
                                    <td><?php echo htmlspecialchars($row['notes'] ?: '-'); ?></td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>

                    <div class="chart-container">
                        <canvas id="feesChart" width="400" height="200"></canvas>
                    </div>
                    <?php else: ?>
                    <div class="alert alert-success alert-custom">
                        <i class="fas fa-check-circle"></i>
                        لا توجد رسوم إضافية مطبقة على هذا التاجر
                    </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Advanced Analytics Tab -->
            <div class="tab-pane fade" id="analytics-reports">
                <!-- Annual Performance Report -->
                <div class="report-card">
                    <div class="report-header">
                        <i class="fas fa-trophy report-icon text-success"></i>
                        <h3 class="report-title">تقرير الأداء السنوي</h3>
                    </div>
                    <p class="text-muted">تحليل شامل للأداء السنوي مع مقارنة السنوات</p>

                    <?php if (!empty($annual_data)): ?>
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>السنة</th>
                                    <th>عدد الإيصالات</th>
                                    <th>إجمالي الإيصالات</th>
                                    <th>عدد المدفوعات</th>
                                    <th>إجمالي المدفوعات</th>
                                    <th>إجمالي المصروفات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($annual_data as $row): ?>
                                <tr>
                                    <td class="fw-bold"><?php echo $row['year']; ?></td>
                                    <td><?php echo $row['receipt_count']; ?></td>
                                    <td class="text-success"><?php echo formatCurrency($row['total_receipts']); ?></td>
                                    <td><?php echo $row['payment_count']; ?></td>
                                    <td class="text-warning"><?php echo formatCurrency($row['total_payments']); ?></td>
                                    <td class="text-danger"><?php echo formatCurrency($row['total_expenses']); ?></td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>

                    <div class="chart-container">
                        <canvas id="annualChart" width="400" height="200"></canvas>
                    </div>
                    <?php else: ?>
                    <div class="alert alert-info alert-custom">
                        <i class="fas fa-info-circle"></i>
                        لا توجد بيانات سنوية كافية لهذا التاجر
                    </div>
                    <?php endif; ?>
                </div>

                <!-- Cash Flow Report -->
                <div class="report-card">
                    <div class="report-header">
                        <i class="fas fa-exchange-alt report-icon text-primary"></i>
                        <h3 class="report-title">تقرير التدفق النقدي</h3>
                    </div>
                    <p class="text-muted">تحليل التدفق النقدي الشهري للتاجر</p>

                    <?php if (!empty($cash_flow_data)): ?>
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>الشهر</th>
                                    <th>التدفق الداخل</th>
                                    <th>التدفق الخارج</th>
                                    <th>صافي التدفق النقدي</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($cash_flow_data as $row): ?>
                                <tr>
                                    <td><?php echo $row['month']; ?></td>
                                    <td class="text-success"><?php echo formatCurrency($row['cash_inflow']); ?></td>
                                    <td class="text-danger"><?php echo formatCurrency($row['cash_outflow']); ?></td>
                                    <td class="fw-bold <?php echo $row['net_cash_flow'] >= 0 ? 'text-success' : 'text-danger'; ?>">
                                        <?php echo formatCurrency($row['net_cash_flow']); ?>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>

                    <div class="chart-container">
                        <canvas id="cashFlowChart" width="400" height="200"></canvas>
                    </div>
                    <?php else: ?>
                    <div class="alert alert-info alert-custom">
                        <i class="fas fa-info-circle"></i>
                        لا توجد بيانات تدفق نقدي لهذا التاجر
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <?php else: ?>
        <!-- No Trader Selected -->
        <div class="alert alert-warning alert-custom text-center">
            <i class="fas fa-user-times fa-3x mb-3"></i>
            <h4>يرجى اختيار التاجر</h4>
            <p>اختر التاجر من القائمة المنسدلة أعلاه لعرض التقارير الخاصة به</p>
        </div>
        <?php endif; ?>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JavaScript -->
    <script>
        // Chart data from PHP
        const monthlyData = <?php echo json_encode($monthly_data); ?>;
        const containerData = <?php echo json_encode($container_data); ?>;
        const feesData = <?php echo json_encode($fees_data); ?>;
        const annualData = <?php echo json_encode($annual_data); ?>;
        const cashFlowData = <?php echo json_encode($cash_flow_data); ?>;

        // Initialize charts when document is ready
        document.addEventListener('DOMContentLoaded', function() {
            if (monthlyData && monthlyData.length > 0) {
                initializeMonthlyChart();
            }
            if (containerData && containerData.length > 0) {
                initializeProfitabilityChart();
            }
            if (feesData && feesData.length > 0) {
                initializeFeesChart();
            }
            if (annualData && annualData.length > 0) {
                initializeAnnualChart();
            }
            if (cashFlowData && cashFlowData.length > 0) {
                initializeCashFlowChart();
            }
        });

        // Initialize monthly income/expense chart
        function initializeMonthlyChart() {
            const ctx = document.getElementById('monthlyChart').getContext('2d');

            const labels = monthlyData.map(item => item.month_name + ' ' + item.year);
            const receipts = monthlyData.map(item => parseInt(item.total_receipts));
            const payments = monthlyData.map(item => parseInt(item.total_payments));
            const expenses = monthlyData.map(item => parseInt(item.total_expenses));

            new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: labels.reverse(),
                    datasets: [{
                        label: 'الإيرادات',
                        data: receipts.reverse(),
                        backgroundColor: 'rgba(52, 152, 219, 0.8)',
                        borderColor: 'rgba(52, 152, 219, 1)',
                        borderWidth: 2
                    }, {
                        label: 'المدفوعات',
                        data: payments.reverse(),
                        backgroundColor: 'rgba(241, 196, 15, 0.8)',
                        borderColor: 'rgba(241, 196, 15, 1)',
                        borderWidth: 2
                    }, {
                        label: 'المصروفات',
                        data: expenses.reverse(),
                        backgroundColor: 'rgba(231, 76, 60, 0.8)',
                        borderColor: 'rgba(231, 76, 60, 1)',
                        borderWidth: 2
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        title: {
                            display: true,
                            text: 'الإيرادات والمصروفات الشهرية'
                        },
                        legend: {
                            position: 'top'
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return new Intl.NumberFormat('ar-SA').format(value) + ' ريال';
                                }
                            }
                        }
                    }
                }
            });
        }

        // Initialize profitability chart
        function initializeProfitabilityChart() {
            const ctx = document.getElementById('profitabilityChart').getContext('2d');

            const labels = containerData.map(item => item.container_number);
            const profits = containerData.map(item => parseInt(item.net_profit));

            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: labels,
                    datasets: [{
                        label: 'صافي الربح',
                        data: profits,
                        backgroundColor: 'rgba(46, 204, 113, 0.2)',
                        borderColor: 'rgba(46, 204, 113, 1)',
                        borderWidth: 3,
                        fill: true,
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        title: {
                            display: true,
                            text: 'ربحية الحاويات'
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return new Intl.NumberFormat('ar-SA').format(value) + ' ريال';
                                }
                            }
                        }
                    }
                }
            });
        }

        // Initialize fees chart
        function initializeFeesChart() {
            const ctx = document.getElementById('feesChart').getContext('2d');

            // Group fees by type
            const feeTypes = {};
            feesData.forEach(item => {
                if (feeTypes[item.fee_type_ar]) {
                    feeTypes[item.fee_type_ar] += parseInt(item.amount);
                } else {
                    feeTypes[item.fee_type_ar] = parseInt(item.amount);
                }
            });

            const labels = Object.keys(feeTypes);
            const amounts = Object.values(feeTypes);

            new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: labels,
                    datasets: [{
                        data: amounts,
                        backgroundColor: [
                            'rgba(52, 152, 219, 0.8)',
                            'rgba(46, 204, 113, 0.8)',
                            'rgba(241, 196, 15, 0.8)',
                            'rgba(155, 89, 182, 0.8)',
                            'rgba(231, 76, 60, 0.8)'
                        ],
                        borderWidth: 2
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        title: {
                            display: true,
                            text: 'توزيع أنواع الرسوم'
                        },
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }

        // Initialize annual chart
        function initializeAnnualChart() {
            const ctx = document.getElementById('annualChart').getContext('2d');

            const labels = annualData.map(item => item.year);
            const receipts = annualData.map(item => parseInt(item.total_receipts));
            const payments = annualData.map(item => parseInt(item.total_payments));

            new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: labels.reverse(),
                    datasets: [{
                        label: 'إجمالي الإيرادات',
                        data: receipts.reverse(),
                        backgroundColor: 'rgba(52, 152, 219, 0.8)',
                        borderColor: 'rgba(52, 152, 219, 1)',
                        borderWidth: 2
                    }, {
                        label: 'إجمالي المدفوعات',
                        data: payments.reverse(),
                        backgroundColor: 'rgba(46, 204, 113, 0.8)',
                        borderColor: 'rgba(46, 204, 113, 1)',
                        borderWidth: 2
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        title: {
                            display: true,
                            text: 'الأداء السنوي'
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return new Intl.NumberFormat('ar-SA').format(value) + ' ريال';
                                }
                            }
                        }
                    }
                }
            });
        }

        // Initialize cash flow chart
        function initializeCashFlowChart() {
            const ctx = document.getElementById('cashFlowChart').getContext('2d');

            const labels = cashFlowData.map(item => item.month);
            const inflow = cashFlowData.map(item => parseInt(item.cash_inflow));
            const outflow = cashFlowData.map(item => parseInt(item.cash_outflow));

            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: labels.reverse(),
                    datasets: [{
                        label: 'التدفق الداخل',
                        data: inflow.reverse(),
                        backgroundColor: 'rgba(46, 204, 113, 0.2)',
                        borderColor: 'rgba(46, 204, 113, 1)',
                        borderWidth: 3,
                        fill: false
                    }, {
                        label: 'التدفق الخارج',
                        data: outflow.reverse(),
                        backgroundColor: 'rgba(231, 76, 60, 0.2)',
                        borderColor: 'rgba(231, 76, 60, 1)',
                        borderWidth: 3,
                        fill: false
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        title: {
                            display: true,
                            text: 'التدفق النقدي الشهري'
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return new Intl.NumberFormat('ar-SA').format(value) + ' ريال';
                                }
                            }
                        }
                    }
                }
            });
        }

        // Export to Excel function
        function exportToExcel() {
            // Create a simple CSV export
            let csvContent = "data:text/csv;charset=utf-8,";

            // Add headers
            csvContent += "تاريخ المعاملة,نوع المعاملة,المبلغ,الملاحظات\n";

            // Add financial data if available
            if (typeof financialData !== 'undefined' && financialData.length > 0) {
                financialData.forEach(row => {
                    csvContent += `${row.document_date},${row.document_type_ar},${row.amount},"${row.notes || ''}"\n`;
                });
            }

            // Create download link
            const encodedUri = encodeURI(csvContent);
            const link = document.createElement("a");
            link.setAttribute("href", encodedUri);
            link.setAttribute("download", "trader_report.csv");
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            // Show success message
            showNotification('تم تصدير التقرير بنجاح', 'success');
        }

        // Show notification function
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; max-width: 300px;';
            notification.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(notification);

            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 3000);
        }

        // Print styles
        const printStyles = `
            @media print {
                .nav-pills, .trader-selector, .btn { display: none !important; }
                .main-container { margin: 0; padding: 20px; }
                .report-card { break-inside: avoid; margin-bottom: 30px; }
                .chart-container { display: none; }
            }
        `;

        const styleSheet = document.createElement("style");
        styleSheet.type = "text/css";
        styleSheet.innerText = printStyles;
        document.head.appendChild(styleSheet);
    </script>
</body>
</html>
