-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- Generation Time: Aug 18, 2025 at 11:12 AM
-- Server version: 10.4.32-MariaDB
-- PHP Version: 8.2.12

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `ccis_system`
--

DELIMITER $$
--
-- Procedures
--
CREATE DEFINER=`root`@`localhost` PROCEDURE `create_financial_entry` (IN `doc_id` INT, IN `fund_type_id` INT, IN `doc_type` ENUM('receipt','payment','expense','transfer','capital'), IN `doc_date` DATE, IN `amount` INT, IN `trader_id` INT)   BEGIN
    DECLARE voucher_id INT;
    DECLARE fund_account_id INT;
    DECLARE trader_fund_id INT;
    
    -- الحصول على معرف صندوق التاجر
    SELECT ff.id INTO trader_fund_id 
    FROM financial_funds ff 
    WHERE ff.id = fund_type_id;
    
    -- الحصول على معرف حساب التاجر من خلال الكود
    SELECT f.id INTO fund_account_id 
    FROM financial_funds f
    INNER JOIN traders t ON f.code = t.code
    WHERE t.id = trader_id;
    
    -- إنشاء قسيمة جديدة
    INSERT INTO voucher (no, type_id, date) 
    VALUES (doc_id, 1, doc_date);
    
    -- الحصول على معرف القسيمة المنشأة
    SET voucher_id = LAST_INSERT_ID();
    
    -- تطبيق القيود المحاسبية حسب نوع المستند
    IF doc_type = 'receipt' THEN
        -- إيصال استلام: مدين الصندوق، دائن حساب العميل
        INSERT INTO voucher_account (voucher_id, date, depit, credit, note, account_id)
        VALUES 
            (voucher_id, doc_date, amount, 0, CONCAT('إيصال استلام رقم ', doc_id), trader_fund_id),
            (voucher_id, doc_date, 0, amount, CONCAT('إيصال استلام من العميل ', trader_id), fund_account_id);
            
    ELSEIF doc_type = 'payment' THEN
        -- إيصال دفع: دائن الصندوق، مدين حساب العميل
        INSERT INTO voucher_account (voucher_id, date, depit, credit, note, account_id)
        VALUES 
            (voucher_id, doc_date, 0, amount, CONCAT('إيصال دفع رقم ', doc_id), trader_fund_id),
            (voucher_id, doc_date, amount, 0, CONCAT('إيصال دفع للعميل ', trader_id), fund_account_id);
            
    ELSEIF doc_type = 'expense' THEN
        -- مصروف: دائن الصندوق، مدين حساب المصروفات
        INSERT INTO voucher_account (voucher_id, date, depit, credit, note, account_id)
        VALUES 
            (voucher_id, doc_date, 0, amount, CONCAT('مصروف رقم ', doc_id), trader_fund_id),
            (voucher_id, doc_date, amount, 0, CONCAT('مصروف للعميل ', trader_id), fund_account_id);
            
    ELSEIF doc_type = 'transfer' THEN
        -- تحويل: مدين الصندوق المستقبل، دائن الصندوق المرسل
        INSERT INTO voucher_account (voucher_id, date, depit, credit, note, account_id)
        VALUES 
            (voucher_id, doc_date, amount, 0, CONCAT('تحويل وارد رقم ', doc_id), fund_account_id),
            (voucher_id, doc_date, 0, amount, CONCAT('تحويل صادر رقم ', doc_id), trader_fund_id);
            
    ELSEIF doc_type = 'capital' THEN
        -- رأس مال: مدين الصندوق، دائن رأس المال
        INSERT INTO voucher_account (voucher_id, date, depit, credit, note, account_id)
        VALUES 
            (voucher_id, doc_date, amount, 0, CONCAT('رأس مال رقم ', doc_id), trader_fund_id),
            (voucher_id, doc_date, 0, amount, CONCAT('رأس مال من ', trader_id), fund_account_id);
    END IF;
    
END$$

--
-- Functions
--
CREATE DEFINER=`root`@`localhost` FUNCTION `get_next_document_number` (`doc_type_code` VARCHAR(10)) RETURNS VARCHAR(20) CHARSET utf8mb4 COLLATE utf8mb4_general_ci DETERMINISTIC READS SQL DATA BEGIN
    DECLARE next_seq INT;
    DECLARE doc_prefix VARCHAR(10);
    DECLARE seq_length INT;
    DECLARE formatted_number VARCHAR(20);
    
    -- الحصول على البيانات والرقم التالي
    SELECT current_sequence + 1, prefix, sequence_length 
    INTO next_seq, doc_prefix, seq_length
    FROM document_types 
    WHERE type_code = doc_type_code AND is_active = 1;
    
    -- تحديث الرقم التسلسلي
    UPDATE document_types 
    SET current_sequence = next_seq 
    WHERE type_code = doc_type_code;
    
    -- تنسيق الرقم النهائي
    SET formatted_number = CONCAT(
        doc_prefix, 
        '-', 
        YEAR(CURDATE()), 
        '-', 
        LPAD(next_seq, seq_length, '0')
    );
    
    RETURN formatted_number;
END$$

DELIMITER ;

-- --------------------------------------------------------

--
-- Table structure for table `audittrail`
--

CREATE TABLE `audittrail` (
  `Id` int(11) NOT NULL,
  `DateTime` datetime NOT NULL,
  `Script` varchar(255) DEFAULT NULL,
  `User` varchar(255) DEFAULT NULL,
  `Action` varchar(255) DEFAULT NULL,
  `Table` varchar(255) DEFAULT NULL,
  `Field` varchar(255) DEFAULT NULL,
  `KeyValue` longtext DEFAULT NULL,
  `OldValue` longtext DEFAULT NULL,
  `NewValue` longtext DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `audittrail`
--

INSERT INTO `audittrail` (`Id`, `DateTime`, `Script`, `User`, `Action`, `Table`, `Field`, `KeyValue`, `OldValue`, `NewValue`) VALUES
(1, '2025-07-30 14:25:19', '/sccisbasrah/login', '-1', 'تسجيل دخول تلقائي', '::1', '', '', NULL, NULL),
(2, '2025-07-30 14:25:19', '/sccisbasrah/login', '-1', 'تسجيل دخول', '::1', '', '', NULL, NULL),
(3, '2025-07-30 19:54:27', '/sccisbasrah/companiesedit', '-1', 'U', 'companies', 'name', '1', '22', 'فحص'),
(4, '2025-07-30 20:07:36', '/sccisbasrah/logout', '-2', 'تسجيل خروج', '::1', '', '', NULL, NULL),
(5, '2025-07-30 20:07:58', '/sccisbasrah/login', '-1', 'تسجيل دخول تلقائي', '::1', '', '', NULL, NULL),
(6, '2025-07-30 20:07:58', '/sccisbasrah/login', '-1', 'تسجيل دخول', '::1', '', '', NULL, NULL),
(7, '2025-07-31 08:41:02', '/sccisbasrah/containersadd', '-1', 'A', 'containers', 'container_number', '1', '', 'mbngh23423423'),
(8, '2025-07-31 08:41:02', '/sccisbasrah/containersadd', '-1', 'A', 'containers', 'company_id', '1', '', '1'),
(9, '2025-07-31 08:41:03', '/sccisbasrah/containersadd', '-1', 'A', 'containers', 'trader_id', '1', '', '1'),
(10, '2025-07-31 08:41:03', '/sccisbasrah/containersadd', '-1', 'A', 'containers', 'entry_date', '1', '', '2025-07-30'),
(11, '2025-07-31 08:41:03', '/sccisbasrah/containersadd', '-1', 'A', 'containers', 'exit_date', '1', '', '2025-07-30'),
(12, '2025-07-31 08:41:03', '/sccisbasrah/containersadd', '-1', 'A', 'containers', 'status', '1', '', 'pending'),
(13, '2025-07-31 08:41:03', '/sccisbasrah/containersadd', '-1', 'A', 'containers', 'continer_content', '1', '', 'dfgdfg'),
(14, '2025-07-31 08:41:04', '/sccisbasrah/containersadd', '-1', 'A', 'containers', 'contyner_size', '1', '', '40'),
(15, '2025-07-31 08:41:04', '/sccisbasrah/containersadd', '-1', 'A', 'containers', 'purchase_price', '1', '', '34234234'),
(16, '2025-07-31 08:41:04', '/sccisbasrah/containersadd', '-1', 'A', 'containers', 'selling_price', '1', '', '0'),
(17, '2025-07-31 08:41:04', '/sccisbasrah/containersadd', '-1', 'A', 'containers', 'notes', '1', '', 'fgdfgfd'),
(18, '2025-07-31 08:41:05', '/sccisbasrah/containersadd', '-1', 'A', 'containers', 'created_by', '1', '', '-1'),
(19, '2025-07-31 08:41:05', '/sccisbasrah/containersadd', '-1', 'A', 'containers', 'created_at', '1', '', '2025-07-31'),
(20, '2025-07-31 08:41:05', '/sccisbasrah/containersadd', '-1', 'A', 'containers', 'id', '1', '', '1'),
(21, '2025-07-31 10:35:21', '/sccisbasrah/containersedit', '-1', 'U', 'containers', 'selling_price', '1', '0', '*********'),
(22, '2025-07-31 10:35:21', '/sccisbasrah/containersedit', '-1', 'U', 'containers', 'updated_at', '1', NULL, '2025-07-31 10:35:21'),
(23, '2025-07-31 10:35:21', '/sccisbasrah/containersedit', '-1', 'U', 'containers', 'updated_by', '1', NULL, '-1'),
(24, '2025-07-31 10:35:35', '/sccisbasrah/containersdelete/1', '-1', '*** بدء الحذف الدفعي ***', 'containers', '', '', NULL, NULL),
(25, '2025-07-31 10:35:35', '/sccisbasrah/containersdelete/1', '-1', 'U', 'containers', 'deleted_at', '1', NULL, '2025/07/31 10:35:35'),
(26, '2025-07-31 10:35:35', '/sccisbasrah/containersdelete/1', '-1', 'D', 'containers', 'id', '1', '1', NULL),
(27, '2025-07-31 10:35:35', '/sccisbasrah/containersdelete/1', '-1', 'D', 'containers', 'container_number', '1', 'mbngh23423423', NULL),
(28, '2025-07-31 10:35:35', '/sccisbasrah/containersdelete/1', '-1', 'D', 'containers', 'company_id', '1', '1', NULL),
(29, '2025-07-31 10:35:35', '/sccisbasrah/containersdelete/1', '-1', 'D', 'containers', 'trader_id', '1', '1', NULL),
(30, '2025-07-31 10:35:35', '/sccisbasrah/containersdelete/1', '-1', 'D', 'containers', 'entry_date', '1', '2025-07-30', NULL),
(31, '2025-07-31 10:35:35', '/sccisbasrah/containersdelete/1', '-1', 'D', 'containers', 'exit_date', '1', '2025-07-30', NULL),
(32, '2025-07-31 10:35:35', '/sccisbasrah/containersdelete/1', '-1', 'D', 'containers', 'status', '1', 'pending', NULL),
(33, '2025-07-31 10:35:35', '/sccisbasrah/containersdelete/1', '-1', 'D', 'containers', 'continer_content', '1', 'dfgdfg', NULL),
(34, '2025-07-31 10:35:35', '/sccisbasrah/containersdelete/1', '-1', 'D', 'containers', 'contyner_size', '1', '40', NULL),
(35, '2025-07-31 10:35:35', '/sccisbasrah/containersdelete/1', '-1', 'D', 'containers', 'purchase_price', '1', '34234234', NULL),
(36, '2025-07-31 10:35:35', '/sccisbasrah/containersdelete/1', '-1', 'D', 'containers', 'selling_price', '1', '*********', NULL),
(37, '2025-07-31 10:35:35', '/sccisbasrah/containersdelete/1', '-1', 'D', 'containers', 'notes', '1', 'fgdfgfd', NULL),
(38, '2025-07-31 10:35:35', '/sccisbasrah/containersdelete/1', '-1', 'D', 'containers', 'created_by', '1', '-1', NULL),
(39, '2025-07-31 10:35:35', '/sccisbasrah/containersdelete/1', '-1', 'D', 'containers', 'created_at', '1', '2025-07-31 00:00:00', NULL),
(40, '2025-07-31 10:35:35', '/sccisbasrah/containersdelete/1', '-1', 'D', 'containers', 'deleted_at', '1', NULL, NULL),
(41, '2025-07-31 10:35:35', '/sccisbasrah/containersdelete/1', '-1', 'D', 'containers', 'deleted_by', '1', NULL, NULL),
(42, '2025-07-31 10:35:35', '/sccisbasrah/containersdelete/1', '-1', 'D', 'containers', 'updated_at', '1', '2025-07-31 10:35:21', NULL),
(43, '2025-07-31 10:35:36', '/sccisbasrah/containersdelete/1', '-1', 'D', 'containers', 'updated_by', '1', '-1', NULL),
(44, '2025-07-31 10:35:36', '/sccisbasrah/containersdelete/1', '-1', '*** تم الحذف الدفعي بنجاح ***', 'containers', '', '', NULL, NULL),
(45, '2025-07-31 10:44:58', '/sccisbasrah/containersdelete/1', '-1', '*** بدء الحذف الدفعي ***', 'containers', '', '', NULL, NULL),
(46, '2025-07-31 10:44:58', '/sccisbasrah/containersdelete/1', '-1', 'U', 'containers', 'deleted_at', '1', NULL, '2025/07/31 10:44:58'),
(47, '2025-07-31 10:44:58', '/sccisbasrah/containersdelete/1', '-1', 'D', 'containers', 'id', '1', '1', NULL),
(48, '2025-07-31 10:44:58', '/sccisbasrah/containersdelete/1', '-1', 'D', 'containers', 'container_number', '1', 'mbngh23423423', NULL),
(49, '2025-07-31 10:44:58', '/sccisbasrah/containersdelete/1', '-1', 'D', 'containers', 'company_id', '1', '1', NULL),
(50, '2025-07-31 10:44:59', '/sccisbasrah/containersdelete/1', '-1', 'D', 'containers', 'trader_id', '1', '1', NULL),
(51, '2025-07-31 10:44:59', '/sccisbasrah/containersdelete/1', '-1', 'D', 'containers', 'entry_date', '1', '2025-07-30', NULL),
(52, '2025-07-31 10:44:59', '/sccisbasrah/containersdelete/1', '-1', 'D', 'containers', 'exit_date', '1', '2025-07-30', NULL),
(53, '2025-07-31 10:44:59', '/sccisbasrah/containersdelete/1', '-1', 'D', 'containers', 'status', '1', 'pending', NULL),
(54, '2025-07-31 10:44:59', '/sccisbasrah/containersdelete/1', '-1', 'D', 'containers', 'continer_content', '1', 'dfgdfg', NULL),
(55, '2025-07-31 10:44:59', '/sccisbasrah/containersdelete/1', '-1', 'D', 'containers', 'contyner_size', '1', '40', NULL),
(56, '2025-07-31 10:44:59', '/sccisbasrah/containersdelete/1', '-1', 'D', 'containers', 'purchase_price', '1', '34234234', NULL),
(57, '2025-07-31 10:44:59', '/sccisbasrah/containersdelete/1', '-1', 'D', 'containers', 'selling_price', '1', '*********', NULL),
(58, '2025-07-31 10:44:59', '/sccisbasrah/containersdelete/1', '-1', 'D', 'containers', 'notes', '1', 'fgdfgfd', NULL),
(59, '2025-07-31 10:44:59', '/sccisbasrah/containersdelete/1', '-1', 'D', 'containers', 'created_by', '1', '-1', NULL),
(60, '2025-07-31 10:44:59', '/sccisbasrah/containersdelete/1', '-1', 'D', 'containers', 'created_at', '1', '2025-07-31 00:00:00', NULL),
(61, '2025-07-31 10:44:59', '/sccisbasrah/containersdelete/1', '-1', 'D', 'containers', 'deleted_at', '1', NULL, NULL),
(62, '2025-07-31 10:44:59', '/sccisbasrah/containersdelete/1', '-1', 'D', 'containers', 'deleted_by', '1', NULL, NULL),
(63, '2025-07-31 10:44:59', '/sccisbasrah/containersdelete/1', '-1', 'D', 'containers', 'updated_at', '1', '2025-07-31 10:35:21', NULL),
(64, '2025-07-31 10:44:59', '/sccisbasrah/containersdelete/1', '-1', 'D', 'containers', 'updated_by', '1', '-1', NULL),
(65, '2025-07-31 10:44:59', '/sccisbasrah/containersdelete/1', '-1', '*** تم الحذف الدفعي بنجاح ***', 'containers', '', '', NULL, NULL),
(66, '2025-07-31 11:58:28', '/sccisbasrah/tradersadd', '-1', 'A', 'traders', 'name', '2', '', 'علي'),
(67, '2025-07-31 11:58:28', '/sccisbasrah/tradersadd', '-1', 'A', 'traders', 'phone', '2', '', '354354'),
(68, '2025-07-31 11:58:28', '/sccisbasrah/tradersadd', '-1', 'A', 'traders', 'email', '2', '', '<EMAIL>'),
(69, '2025-07-31 11:58:28', '/sccisbasrah/tradersadd', '-1', 'A', 'traders', 'address', '2', '', 'shat alarab'),
(70, '2025-07-31 11:58:28', '/sccisbasrah/tradersadd', '-1', 'A', 'traders', 'notes', '2', '', NULL),
(71, '2025-07-31 11:58:28', '/sccisbasrah/tradersadd', '-1', 'A', 'traders', 'created_at', '2', '', '2025-07-31'),
(72, '2025-07-31 11:58:28', '/sccisbasrah/tradersadd', '-1', 'A', 'traders', 'created_by', '2', '', '-1'),
(73, '2025-07-31 11:58:29', '/sccisbasrah/tradersadd', '-1', 'A', 'traders', 'deleted_by', '2', '', NULL),
(74, '2025-07-31 11:58:29', '/sccisbasrah/tradersadd', '-1', 'A', 'traders', 'updated_at', '2', '', NULL),
(75, '2025-07-31 11:58:29', '/sccisbasrah/tradersadd', '-1', 'A', 'traders', 'updated_by', '2', '', NULL),
(76, '2025-07-31 11:58:29', '/sccisbasrah/tradersadd', '-1', 'A', 'traders', 'id', '2', '', '2'),
(77, '2025-08-01 11:48:01', '/sccisbasrah/view2add', '-1', 'A', 'view2', 'fund_type_id', '5', '', '1'),
(78, '2025-08-01 11:48:01', '/sccisbasrah/view2add', '-1', 'A', 'view2', 'document_type', '5', '', 'receipt'),
(79, '2025-08-01 11:48:01', '/sccisbasrah/view2add', '-1', 'A', 'view2', 'document_date', '5', '', '2025-08-01'),
(80, '2025-08-01 11:48:01', '/sccisbasrah/view2add', '-1', 'A', 'view2', 'amount', '5', '', '400000'),
(81, '2025-08-01 11:48:01', '/sccisbasrah/view2add', '-1', 'A', 'view2', 'trader_id', '5', '', '1'),
(82, '2025-08-01 11:48:01', '/sccisbasrah/view2add', '-1', 'A', 'view2', 'notes', '5', '', 'بيلي'),
(83, '2025-08-01 11:48:01', '/sccisbasrah/view2add', '-1', 'A', 'view2', 'id', '5', '', '5'),
(84, '2025-08-01 11:48:29', '/sccisbasrah/view2edit', '-1', 'U', 'view2', 'trader_id', '3', '2', '1'),
(85, '2025-08-01 11:48:50', '/sccisbasrah/view2add', '-1', 'A', 'view2', 'fund_type_id', '6', '', '1'),
(86, '2025-08-01 11:48:50', '/sccisbasrah/view2add', '-1', 'A', 'view2', 'document_type', '6', '', 'receipt'),
(87, '2025-08-01 11:48:50', '/sccisbasrah/view2add', '-1', 'A', 'view2', 'document_date', '6', '', '2025-08-06'),
(88, '2025-08-01 11:48:50', '/sccisbasrah/view2add', '-1', 'A', 'view2', 'amount', '6', '', '300000'),
(89, '2025-08-01 11:48:50', '/sccisbasrah/view2add', '-1', 'A', 'view2', 'trader_id', '6', '', '2'),
(90, '2025-08-01 11:48:50', '/sccisbasrah/view2add', '-1', 'A', 'view2', 'notes', '6', '', 'ؤرلاؤر'),
(91, '2025-08-01 11:48:50', '/sccisbasrah/view2add', '-1', 'A', 'view2', 'id', '6', '', '6'),
(92, '2025-08-01 11:53:35', '/sccisbasrah/view2add', '-1', 'A', 'view2', 'fund_type_id', '7', '', '2'),
(93, '2025-08-01 11:53:35', '/sccisbasrah/view2add', '-1', 'A', 'view2', 'document_type', '7', '', 'receipt'),
(94, '2025-08-01 11:53:35', '/sccisbasrah/view2add', '-1', 'A', 'view2', 'document_date', '7', '', '2025-08-11'),
(95, '2025-08-01 11:53:35', '/sccisbasrah/view2add', '-1', 'A', 'view2', 'amount', '7', '', '300000'),
(96, '2025-08-01 11:53:36', '/sccisbasrah/view2add', '-1', 'A', 'view2', 'trader_id', '7', '', '2'),
(97, '2025-08-01 11:53:36', '/sccisbasrah/view2add', '-1', 'A', 'view2', 'notes', '7', '', 'بيليب'),
(98, '2025-08-01 11:53:36', '/sccisbasrah/view2add', '-1', 'A', 'view2', 'created_by', '7', '', '0'),
(99, '2025-08-01 11:53:36', '/sccisbasrah/view2add', '-1', 'A', 'view2', 'created_at', '7', '', '2025-08-01'),
(100, '2025-08-01 11:53:36', '/sccisbasrah/view2add', '-1', 'A', 'view2', 'deleted_at', '7', '', NULL),
(101, '2025-08-01 11:53:36', '/sccisbasrah/view2add', '-1', 'A', 'view2', 'deleted_by', '7', '', NULL),
(102, '2025-08-01 11:53:36', '/sccisbasrah/view2add', '-1', 'A', 'view2', 'updated_by', '7', '', NULL),
(103, '2025-08-01 11:53:36', '/sccisbasrah/view2add', '-1', 'A', 'view2', 'id', '7', '', '7'),
(104, '2025-08-01 11:58:01', '/sccisbasrah/view2add', '-1', 'A', 'view2', 'fund_type_id', '8', '', '2'),
(105, '2025-08-01 11:58:02', '/sccisbasrah/view2add', '-1', 'A', 'view2', 'document_type', '8', '', 'receipt'),
(106, '2025-08-01 11:58:02', '/sccisbasrah/view2add', '-1', 'A', 'view2', 'document_date', '8', '', '2025-08-18'),
(107, '2025-08-01 11:58:02', '/sccisbasrah/view2add', '-1', 'A', 'view2', 'amount', '8', '', '200000'),
(108, '2025-08-01 11:58:02', '/sccisbasrah/view2add', '-1', 'A', 'view2', 'trader_id', '8', '', '1'),
(109, '2025-08-01 11:58:02', '/sccisbasrah/view2add', '-1', 'A', 'view2', 'notes', '8', '', 'بللارلاىرىرلا'),
(110, '2025-08-01 11:58:02', '/sccisbasrah/view2add', '-1', 'A', 'view2', 'id', '8', '', '8'),
(111, '2025-08-04 09:00:18', '/sccisb/companiesadd', '-1', 'A', 'companies', 'name', '2', '', 'atf'),
(112, '2025-08-04 09:00:18', '/sccisb/companiesadd', '-1', 'A', 'companies', 'phone', '2', '', '32534543'),
(113, '2025-08-04 09:00:18', '/sccisb/companiesadd', '-1', 'A', 'companies', 'manager_name', '2', '', 'sdasdsa'),
(114, '2025-08-04 09:00:18', '/sccisb/companiesadd', '-1', 'A', 'companies', 'email', '2', '', '<EMAIL>'),
(115, '2025-08-04 09:00:18', '/sccisb/companiesadd', '-1', 'A', 'companies', 'address', '2', '', 'xczxzczx'),
(116, '2025-08-04 09:00:18', '/sccisb/companiesadd', '-1', 'A', 'companies', 'taxnumber', '2', '', 'zxczxewr'),
(117, '2025-08-04 09:00:18', '/sccisb/companiesadd', '-1', 'A', 'companies', 'notes', '2', '', NULL),
(118, '2025-08-04 09:00:18', '/sccisb/companiesadd', '-1', 'A', 'companies', 'id', '2', '', '2'),
(119, '2025-08-04 09:01:20', '/sccisb/companiesedit', '-1', 'U', 'companies', 'phone', '1', '07709010579', '077090105794'),
(120, '2025-08-04 09:01:20', '/sccisb/companiesedit', '-1', 'U', 'companies', 'updated_at', '1', NULL, '2025-08-04 09:01:20'),
(121, '2025-08-04 09:01:20', '/sccisb/companiesedit', '-1', 'U', 'companies', 'updated_by', '1', NULL, '-1'),
(122, '2025-08-04 09:06:31', '/sccisb/companiesadd', '-1', 'A', 'companies', 'name', '3', '', 'atf'),
(123, '2025-08-04 09:06:31', '/sccisb/companiesadd', '-1', 'A', 'companies', 'phone', '3', '', '32534543'),
(124, '2025-08-04 09:06:31', '/sccisb/companiesadd', '-1', 'A', 'companies', 'manager_name', '3', '', 'sdasdsa'),
(125, '2025-08-04 09:06:31', '/sccisb/companiesadd', '-1', 'A', 'companies', 'email', '3', '', '<EMAIL>'),
(126, '2025-08-04 09:06:31', '/sccisb/companiesadd', '-1', 'A', 'companies', 'address', '3', '', 'xczxzczx'),
(127, '2025-08-04 09:06:31', '/sccisb/companiesadd', '-1', 'A', 'companies', 'taxnumber', '3', '', 'zxczxewr'),
(128, '2025-08-04 09:06:31', '/sccisb/companiesadd', '-1', 'A', 'companies', 'notes', '3', '', NULL),
(129, '2025-08-04 09:06:31', '/sccisb/companiesadd', '-1', 'A', 'companies', 'created_by', '3', '', '-1'),
(130, '2025-08-04 09:06:31', '/sccisb/companiesadd', '-1', 'A', 'companies', 'id', '3', '', '3'),
(131, '2025-08-06 05:27:57', '/sccisb/tradersedit', '-1', 'U', 'traders', 'updated_at', '2', NULL, '2025-08-06 05:27:56'),
(132, '2025-08-06 05:27:57', '/sccisb/tradersedit', '-1', 'U', 'traders', 'updated_by', '2', NULL, '-1'),
(133, '2025-08-06 05:27:57', '/sccisb/tradersedit', '-1', 'U', 'traders', 'code', '2', '', 'acc123'),
(134, '2025-08-06 05:29:05', '/sccisb/tradersadd', '-1', 'A', 'traders', 'name', '3', '', 'reda'),
(135, '2025-08-06 05:29:05', '/sccisb/tradersadd', '-1', 'A', 'traders', 'phone', '3', '', '*********'),
(136, '2025-08-06 05:29:05', '/sccisb/tradersadd', '-1', 'A', 'traders', 'email', '3', '', '<EMAIL>'),
(137, '2025-08-06 05:29:05', '/sccisb/tradersadd', '-1', 'A', 'traders', 'address', '3', '', 'dfsfsdfsd'),
(138, '2025-08-06 05:29:05', '/sccisb/tradersadd', '-1', 'A', 'traders', 'notes', '3', '', 'sdfsd'),
(139, '2025-08-06 05:29:05', '/sccisb/tradersadd', '-1', 'A', 'traders', 'created_at', '3', '', '2025-08-06'),
(140, '2025-08-06 05:29:05', '/sccisb/tradersadd', '-1', 'A', 'traders', 'created_by', '3', '', '-1'),
(141, '2025-08-06 05:29:05', '/sccisb/tradersadd', '-1', 'A', 'traders', 'deleted_by', '3', '', NULL),
(142, '2025-08-06 05:29:05', '/sccisb/tradersadd', '-1', 'A', 'traders', 'updated_at', '3', '', NULL),
(143, '2025-08-06 05:29:05', '/sccisb/tradersadd', '-1', 'A', 'traders', 'updated_by', '3', '', NULL),
(144, '2025-08-06 05:29:05', '/sccisb/tradersadd', '-1', 'A', 'traders', 'code', '3', '', 'acc124'),
(145, '2025-08-06 05:29:05', '/sccisb/tradersadd', '-1', 'A', 'traders', 'id', '3', '', '3'),
(146, '2025-08-07 07:40:52', '/sccisb/login', '-1', 'تسجيل دخول تلقائي', '::1', '', '', NULL, NULL),
(147, '2025-08-07 07:40:53', '/sccisb/login', '-1', 'تسجيل دخول', '::1', '', '', NULL, NULL),
(148, '2025-08-10 16:40:53', '/sccisb/tradersadd', '-1', 'A', 'traders', 'code', '4', '', 'ccx11231'),
(149, '2025-08-10 16:40:54', '/sccisb/tradersadd', '-1', 'A', 'traders', 'name', '4', '', 'scsd'),
(150, '2025-08-10 16:40:54', '/sccisb/tradersadd', '-1', 'A', 'traders', 'phone', '4', '', '3453453'),
(151, '2025-08-10 16:40:54', '/sccisb/tradersadd', '-1', 'A', 'traders', 'email', '4', '', '<EMAIL>'),
(152, '2025-08-10 16:40:54', '/sccisb/tradersadd', '-1', 'A', 'traders', 'address', '4', '', 'dfgfdg'),
(153, '2025-08-10 16:40:54', '/sccisb/tradersadd', '-1', 'A', 'traders', 'notes', '4', '', NULL),
(154, '2025-08-10 16:40:54', '/sccisb/tradersadd', '-1', 'A', 'traders', 'created_at', '4', '', '2025-08-10'),
(155, '2025-08-10 16:40:54', '/sccisb/tradersadd', '-1', 'A', 'traders', 'created_by', '4', '', '-1'),
(156, '2025-08-10 16:40:55', '/sccisb/tradersadd', '-1', 'A', 'traders', 'id', '4', '', '4'),
(157, '2025-08-15 17:11:17', '/sccisb/login', '-1', 'تسجيل دخول تلقائي', '::1', '', '', NULL, NULL),
(158, '2025-08-15 17:11:19', '/sccisb/login', '-1', 'تسجيل دخول', '::1', '', '', NULL, NULL),
(159, '2025-08-15 17:11:42', '/sccisb/login', '-1', 'تسجيل دخول تلقائي', '::1', '', '', NULL, NULL),
(160, '2025-08-15 17:11:42', '/sccisb/login', '-1', 'تسجيل دخول', '::1', '', '', NULL, NULL),
(161, '2025-08-16 10:09:37', '/sccisb/login', '-1', 'تسجيل دخول', '::1', '', '', NULL, NULL),
(162, '2025-08-17 19:35:05', '/sccisb/login', '-1', 'تسجيل دخول تلقائي', '::1', '', '', NULL, NULL),
(163, '2025-08-17 19:35:06', '/sccisb/login', '-1', 'تسجيل دخول', '::1', '', '', NULL, NULL),
(164, '2025-08-17 19:56:00', '/sccisb/finesadd', '-1', 'A', 'fines', 'trader_id', '2', '', '2'),
(165, '2025-08-17 19:56:00', '/sccisb/finesadd', '-1', 'A', 'fines', 'container_id', '2', '', '2'),
(166, '2025-08-17 19:56:00', '/sccisb/finesadd', '-1', 'A', 'fines', 'fine_type', '2', '', 'damages'),
(167, '2025-08-17 19:56:00', '/sccisb/finesadd', '-1', 'A', 'fines', 'amount', '2', '', '5000000'),
(168, '2025-08-17 19:56:01', '/sccisb/finesadd', '-1', 'A', 'fines', 'fine_date', '2', '', '2025-08-06'),
(169, '2025-08-17 19:56:01', '/sccisb/finesadd', '-1', 'A', 'fines', 'notes', '2', '', 'fhfghf'),
(170, '2025-08-17 19:56:01', '/sccisb/finesadd', '-1', 'A', 'fines', 'created_by', '2', '', '-1'),
(171, '2025-08-17 19:56:01', '/sccisb/finesadd', '-1', 'A', 'fines', 'id', '2', '', '2'),
(172, '2025-08-18 06:56:36', '/sccisb/login', '-1', 'تسجيل دخول تلقائي', '::1', '', '', NULL, NULL),
(173, '2025-08-18 06:56:37', '/sccisb/login', '-1', 'تسجيل دخول', '::1', '', '', NULL, NULL),
(174, '2025-08-18 07:16:07', '/sccisb/financialdocumentsadd', '-1', 'A', 'financial_documents', 'fund_type_id', '22', '', '2'),
(175, '2025-08-18 07:16:07', '/sccisb/financialdocumentsadd', '-1', 'A', 'financial_documents', 'document_type', '22', '', 'receipt'),
(176, '2025-08-18 07:16:07', '/sccisb/financialdocumentsadd', '-1', 'A', 'financial_documents', 'document_date', '22', '', '2025-08-18'),
(177, '2025-08-18 07:16:07', '/sccisb/financialdocumentsadd', '-1', 'A', 'financial_documents', 'amount', '22', '', '500000'),
(178, '2025-08-18 07:16:07', '/sccisb/financialdocumentsadd', '-1', 'A', 'financial_documents', 'trader_id', '22', '', '3'),
(179, '2025-08-18 07:16:07', '/sccisb/financialdocumentsadd', '-1', 'A', 'financial_documents', 'notes', '22', '', NULL),
(180, '2025-08-18 07:16:07', '/sccisb/financialdocumentsadd', '-1', 'A', 'financial_documents', 'created_by', '22', '', '-1'),
(181, '2025-08-18 07:16:07', '/sccisb/financialdocumentsadd', '-1', 'A', 'financial_documents', 'id', '22', '', '22'),
(182, '2025-08-18 07:17:58', '/sccisb/financialdocumentsadd', '-1', 'A', 'financial_documents', 'fund_type_id', '23', '', '3'),
(183, '2025-08-18 07:17:58', '/sccisb/financialdocumentsadd', '-1', 'A', 'financial_documents', 'document_type', '23', '', 'payment'),
(184, '2025-08-18 07:17:58', '/sccisb/financialdocumentsadd', '-1', 'A', 'financial_documents', 'document_date', '23', '', '2025-08-18'),
(185, '2025-08-18 07:17:58', '/sccisb/financialdocumentsadd', '-1', 'A', 'financial_documents', 'amount', '23', '', '30000'),
(186, '2025-08-18 07:17:58', '/sccisb/financialdocumentsadd', '-1', 'A', 'financial_documents', 'trader_id', '23', '', '3'),
(187, '2025-08-18 07:17:59', '/sccisb/financialdocumentsadd', '-1', 'A', 'financial_documents', 'notes', '23', '', 'رىلال'),
(188, '2025-08-18 07:17:59', '/sccisb/financialdocumentsadd', '-1', 'A', 'financial_documents', 'created_by', '23', '', '-1'),
(189, '2025-08-18 07:17:59', '/sccisb/financialdocumentsadd', '-1', 'A', 'financial_documents', 'id', '23', '', '23'),
(190, '2025-08-18 07:27:49', '/sccisb/finesadd', '-1', 'A', 'fines', 'trader_id', '3', '', '3'),
(191, '2025-08-18 07:27:49', '/sccisb/finesadd', '-1', 'A', 'fines', 'container_id', '3', '', '3'),
(192, '2025-08-18 07:27:49', '/sccisb/finesadd', '-1', 'A', 'fines', 'fine_type', '3', '', 'damages'),
(193, '2025-08-18 07:27:49', '/sccisb/finesadd', '-1', 'A', 'fines', 'amount', '3', '', '500000'),
(194, '2025-08-18 07:27:50', '/sccisb/finesadd', '-1', 'A', 'fines', 'fine_date', '3', '', '2025-08-20'),
(195, '2025-08-18 07:27:50', '/sccisb/finesadd', '-1', 'A', 'fines', 'notes', '3', '', 'يبلبي'),
(196, '2025-08-18 07:27:50', '/sccisb/finesadd', '-1', 'A', 'fines', 'created_by', '3', '', '-1'),
(197, '2025-08-18 07:27:50', '/sccisb/finesadd', '-1', 'A', 'fines', 'id', '3', '', '3'),
(198, '2025-08-18 07:41:12', '/sccisb/otherfeesadd', '-1', 'A', 'other_fees', 'trader_id', '1', '', '3'),
(199, '2025-08-18 07:41:13', '/sccisb/otherfeesadd', '-1', 'A', 'other_fees', 'container_id', '1', '', '3'),
(200, '2025-08-18 07:41:13', '/sccisb/otherfeesadd', '-1', 'A', 'other_fees', 'types_fee', '1', '', 'returns and fees'),
(201, '2025-08-18 07:41:13', '/sccisb/otherfeesadd', '-1', 'A', 'other_fees', 'amount', '1', '', '500000'),
(202, '2025-08-18 07:41:13', '/sccisb/otherfeesadd', '-1', 'A', 'other_fees', 'fee_date', '1', '', '2025-08-07'),
(203, '2025-08-18 07:41:13', '/sccisb/otherfeesadd', '-1', 'A', 'other_fees', 'notes', '1', '', 'nhjbj'),
(204, '2025-08-18 07:41:14', '/sccisb/otherfeesadd', '-1', 'A', 'other_fees', 'created_by', '1', '', '-1'),
(205, '2025-08-18 07:41:14', '/sccisb/otherfeesadd', '-1', 'A', 'other_fees', 'id', '1', '', '1');

-- --------------------------------------------------------

--
-- Table structure for table `companies`
--

CREATE TABLE `companies` (
  `id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `phone` varchar(50) NOT NULL,
  `manager_name` varchar(255) NOT NULL,
  `email` varchar(50) NOT NULL,
  `address` varchar(255) NOT NULL,
  `taxnumber` varchar(50) DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `created_by` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `deleted_at` datetime DEFAULT NULL,
  `deleted_by` int(11) DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `updated_by` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `companies`
--

INSERT INTO `companies` (`id`, `name`, `phone`, `manager_name`, `email`, `address`, `taxnumber`, `notes`, `created_by`, `created_at`, `deleted_at`, `deleted_by`, `updated_at`, `updated_by`) VALUES
(1, 'فحص', '077090105794', 'شمه يبيسبيس', '<EMAIL>', 'العراق , البصرة , شارع الجزائر565', '435435', NULL, -1, '2025-07-29 21:00:00', NULL, NULL, '2025-08-04 09:01:20', -1),
(2, 'atf', '32534543', 'sdasdsa', '<EMAIL>', 'xczxzczx', 'zxczxewr', NULL, 0, '2025-08-04 09:00:18', NULL, NULL, NULL, NULL),
(3, 'atf', '32534543', 'sdasdsa', '<EMAIL>', 'xczxzczx', 'zxczxewr', NULL, -1, '2025-08-04 09:06:30', NULL, NULL, NULL, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `containers`
--

CREATE TABLE `containers` (
  `id` int(11) NOT NULL,
  `container_number` varchar(50) NOT NULL,
  `company_id` int(11) NOT NULL,
  `trader_id` int(11) NOT NULL,
  `entry_date` date NOT NULL,
  `exit_date` date NOT NULL,
  `status` enum('pending','in_progress','completed','cancelled') NOT NULL DEFAULT 'pending',
  `continer_content` varchar(200) NOT NULL,
  `contyner_size` enum('20','40','45','') NOT NULL,
  `purchase_price` int(50) NOT NULL,
  `selling_price` int(50) NOT NULL,
  `notes` text DEFAULT NULL,
  `created_by` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `deleted_at` datetime DEFAULT NULL,
  `deleted_by` int(11) DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `updated_by` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `containers`
--

INSERT INTO `containers` (`id`, `container_number`, `company_id`, `trader_id`, `entry_date`, `exit_date`, `status`, `continer_content`, `contyner_size`, `purchase_price`, `selling_price`, `notes`, `created_by`, `created_at`, `deleted_at`, `deleted_by`, `updated_at`, `updated_by`) VALUES
(1, 'mbngh23423423', 1, 1, '2025-07-30', '2025-07-30', 'pending', 'dfgdfg', '40', 34234234, *********, 'fgdfgfd', -1, '2025-07-30 21:00:00', NULL, NULL, '2025-07-31 10:35:21', -1);

-- --------------------------------------------------------

--
-- Stand-in structure for view `customer_balances`
-- (See below for the actual view)
--
CREATE TABLE `customer_balances` (
`customer_id` int(11)
,`customer_name` varchar(255)
,`phone` varchar(50)
,`email` varchar(50)
,`address` varchar(255)
,`total_receipts` decimal(32,0)
,`total_payments` decimal(32,0)
,`balance` decimal(33,0)
,`last_transaction_date` date
,`total_transactions` bigint(21)
);

-- --------------------------------------------------------

--
-- Stand-in structure for view `documents_with_numbers`
-- (See below for the actual view)
--
CREATE TABLE `documents_with_numbers` (
`voucher_id` int(11)
,`original_id` varchar(11)
,`document_number` varchar(20)
,`document_type_code` varchar(10)
,`document_type_name` varchar(50)
,`document_date` date
,`total_debit` double(19,2)
,`total_credit` double(19,2)
,`involved_accounts` mediumtext
);

-- --------------------------------------------------------

--
-- Table structure for table `document_types`
--

CREATE TABLE `document_types` (
  `id` int(11) NOT NULL,
  `type_name` varchar(50) NOT NULL,
  `type_code` varchar(10) NOT NULL,
  `prefix` varchar(10) NOT NULL COMMENT 'بادئة رقم المستند',
  `current_sequence` int(11) NOT NULL DEFAULT 1,
  `sequence_length` int(11) NOT NULL DEFAULT 6 COMMENT 'طول رقم التسلسل',
  `is_active` tinyint(1) NOT NULL DEFAULT 1
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `document_types`
--

INSERT INTO `document_types` (`id`, `type_name`, `type_code`, `prefix`, `current_sequence`, `sequence_length`, `is_active`) VALUES
(1, 'إيصال استلام', 'RECEIPT', 'REC', 4, 6, 1),
(2, 'إيصال دفع', 'PAYMENT', 'PAY', 6, 6, 1),
(3, 'مصروف', 'EXPENSE', 'EXP', 4, 6, 1),
(4, 'تحويل', 'TRANSFER', 'TRF', 1, 6, 1),
(5, 'رأس مال', 'CAPITAL', 'CAP', 1, 6, 1),
(6, 'غرامة تأخير', 'FINE_DELAY', 'FDL', 2, 6, 1),
(7, 'غرامة أضرار', 'FINE_DAMAG', 'FDM', 3, 6, 1),
(8, 'غرامة أخرى', 'FINE_OTHER', 'FOT', 1, 6, 1),
(9, 'رسوم موقع', 'FEE_LOCATI', 'FLO', 1, 6, 1),
(10, 'رسوم مناولة', 'FEE_HANDLI', 'FHA', 1, 6, 1),
(11, 'رسوم إرجاع', 'FEE_RETURN', 'FRE', 2, 6, 1),
(12, 'رسوم أخرى', 'FEE_OTHER', 'FOT', 1, 6, 1);

-- --------------------------------------------------------

--
-- Table structure for table `exportlog`
--

CREATE TABLE `exportlog` (
  `FileId` varchar(36) NOT NULL,
  `DateTime` datetime NOT NULL,
  `User` varchar(255) NOT NULL,
  `ExportType` varchar(255) NOT NULL,
  `Table` varchar(255) NOT NULL,
  `KeyValue` varchar(255) DEFAULT NULL,
  `Filename` varchar(255) NOT NULL,
  `Request` longtext NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `financial_documents`
--

CREATE TABLE `financial_documents` (
  `id` int(11) NOT NULL,
  `fund_type_id` int(11) NOT NULL,
  `document_type` enum('receipt','payment','expense','transfer','capital') NOT NULL,
  `document_date` date NOT NULL,
  `amount` int(11) NOT NULL,
  `trader_id` int(11) NOT NULL,
  `notes` text DEFAULT NULL,
  `created_by` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `deleted_at` datetime DEFAULT NULL,
  `deleted_by` int(11) DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `updated_by` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `financial_documents`
--

INSERT INTO `financial_documents` (`id`, `fund_type_id`, `document_type`, `document_date`, `amount`, `trader_id`, `notes`, `created_by`, `created_at`, `deleted_at`, `deleted_by`, `updated_at`, `updated_by`) VALUES
(1, 0, 'receipt', '2025-07-31', 23543534, 1, 'ertret', -1, '2025-07-01 21:00:00', NULL, NULL, NULL, NULL),
(2, 0, 'payment', '2025-07-31', 5000000, 1, NULL, -1, '2025-07-30 21:00:00', NULL, NULL, NULL, NULL),
(3, 0, 'receipt', '2025-07-09', 500000, 1, 'نةننتىنت', -1, '2025-07-30 21:00:00', NULL, NULL, NULL, NULL),
(4, 1, 'receipt', '2025-07-16', 35345, 2, NULL, -1, '2025-07-30 21:00:00', NULL, NULL, NULL, NULL),
(5, 1, 'receipt', '2025-08-01', 400000, 1, 'بيلي', 0, '2025-08-01 11:48:01', NULL, NULL, NULL, NULL),
(6, 1, 'receipt', '2025-08-06', 300000, 2, 'ؤرلاؤر', 0, '2025-08-01 11:48:50', NULL, NULL, NULL, NULL),
(7, 2, 'receipt', '2025-08-11', 300000, 2, 'بيليب', 0, '2025-07-31 21:00:00', NULL, NULL, NULL, NULL),
(8, 2, 'receipt', '2025-08-18', 200000, 1, 'بللارلاىرىرلا', 0, '2025-08-01 11:58:01', NULL, NULL, NULL, NULL),
(9, 2, 'receipt', '2025-08-06', 8000, 1, 'ffbdfg', -1, '2025-08-06 21:00:00', NULL, NULL, NULL, NULL),
(11, 2, 'receipt', '2025-08-06', 50000, 3, 'تانتنلت', -1, '2025-08-06 21:00:00', NULL, NULL, NULL, NULL),
(12, 1, 'payment', '2025-08-06', 1500000, 2, NULL, -1, '2025-08-06 21:00:00', NULL, NULL, NULL, NULL),
(13, 2, 'expense', '2025-08-07', 100000, 1, NULL, -1, '2025-08-06 21:00:00', NULL, NULL, NULL, NULL),
(14, 2, 'expense', '2025-08-07', 200000, 1, NULL, -1, '2025-08-06 21:00:00', NULL, NULL, NULL, NULL),
(15, 2, 'payment', '2025-08-08', 300000, 1, NULL, -1, '2025-08-06 21:00:00', NULL, NULL, NULL, NULL),
(16, 4, 'payment', '2025-08-09', 400000, 1, NULL, -1, '2025-08-08 21:00:00', NULL, NULL, NULL, NULL),
(17, 2, 'receipt', '2025-08-10', 300000, 3, 'يصيسبسي', -1, '2025-08-09 21:00:00', NULL, NULL, NULL, NULL),
(18, 3, 'payment', '2025-08-10', 500000, 1, NULL, -1, '2025-08-09 21:00:00', NULL, NULL, NULL, NULL),
(19, 3, 'receipt', '2025-08-15', 500000, 1, NULL, -1, '2025-08-16 11:05:49', NULL, NULL, NULL, NULL),
(20, 3, 'payment', '2025-08-15', 400000, 1, NULL, -1, '2025-08-16 11:06:19', NULL, NULL, NULL, NULL),
(21, 3, 'expense', '2025-08-15', 700000, 1, NULL, -1, '2025-08-16 11:06:41', NULL, NULL, NULL, NULL),
(22, 2, 'receipt', '2025-08-18', 500000, 3, NULL, -1, '2025-08-18 07:16:07', NULL, NULL, NULL, NULL),
(23, 3, 'payment', '2025-08-18', 30000, 3, 'رىلال', -1, '2025-08-18 07:17:58', NULL, NULL, NULL, NULL);

--
-- Triggers `financial_documents`
--
DELIMITER $$
CREATE TRIGGER `after_financial_doc_insert` AFTER INSERT ON `financial_documents` FOR EACH ROW BEGIN
    DECLARE voucher_id INT;
    DECLARE fund_account_id INT;
    DECLARE trader_fund_id INT;
    DECLARE doc_type_code VARCHAR(10);
    DECLARE doc_number VARCHAR(20);
    
    -- تحديد كود نوع المستند
    SET doc_type_code = CASE NEW.document_type
        WHEN 'receipt' THEN 'RECEIPT'
        WHEN 'payment' THEN 'PAYMENT'
        WHEN 'expense' THEN 'EXPENSE'
        WHEN 'transfer' THEN 'TRANSFER'
        WHEN 'capital' THEN 'CAPITAL'
        ELSE 'RECEIPT'
    END;
    
    -- توليد رقم المستند
    SET doc_number = get_next_document_number(doc_type_code);
    
    -- الحصول على معرف صندوق التاجر
    SELECT ff.id INTO trader_fund_id 
    FROM financial_funds ff
    WHERE ff.id = NEW.fund_type_id;
    
    -- الحصول على معرف حساب التاجر من خلال الكود
    SELECT f.id INTO fund_account_id 
    FROM financial_funds f
    INNER JOIN traders t ON f.code = t.code
    WHERE t.id = NEW.trader_id;
    
    -- إنشاء قسيمة جديدة مع الرقم المخصص
    INSERT INTO voucher (no, document_number, document_type_code, type_id, date)
    VALUES (NEW.id, doc_number, doc_type_code, 1, NEW.document_date);
    
    SET voucher_id = LAST_INSERT_ID();
    
    -- تطبيق القيود المحاسبية
    IF NEW.document_type = 'receipt' THEN
        INSERT INTO voucher_account (voucher_id, date, depit, credit, note, account_id)
        VALUES 
            (voucher_id, NEW.document_date, NEW.amount, 0.00, CONCAT('إيصال استلام ', doc_number), trader_fund_id),
            (voucher_id, NEW.document_date,  0.00, NEW.amount, CONCAT('من العميل - ', doc_number), NEW.fund_type_id);
            
            
            
    ELSEIF NEW.document_type = 'payment' THEN
        INSERT INTO voucher_account (voucher_id, date, depit, credit, note, account_id)
        VALUES 
            (voucher_id, NEW.document_date,  0.00, NEW.amount, CONCAT('إيصال دفع ', doc_number), trader_fund_id),
            
            (voucher_id, NEW.document_date, NEW.amount, 0.00, CONCAT('للعميل - ', doc_number), NEW.fund_type_id);
            
            
            
    ELSEIF NEW.document_type = 'expense' THEN
        INSERT INTO voucher_account (voucher_id, date, depit, credit, note, account_id)
        VALUES 
            (voucher_id, NEW.document_date, NEW.amount, 0.00, CONCAT('مصروف ', doc_number), trader_fund_id),
            
            (voucher_id, NEW.document_date, 0.00, NEW.amount, CONCAT('مصروف - ', doc_number), NEW.fund_type_id);
            
            
            
    ELSEIF NEW.document_type = 'transfer' THEN
        INSERT INTO voucher_account (voucher_id, date, depit, credit, note, account_id)
        VALUES 
            (voucher_id, NEW.document_date, NEW.amount, 0.00, CONCAT('تحويل وارد ', doc_number), NEW.fund_type_id),
            (voucher_id, NEW.document_date, NEW.document_date, 0.00, CONCAT('تحويل صادر ', doc_number), trader_fund_id);
            
            
    ELSEIF NEW.document_type = 'capital' THEN
        INSERT INTO voucher_account (voucher_id, date, depit, credit, note, account_id)
        VALUES 
            (voucher_id, NEW.document_date,NEW.amount, 0.00, CONCAT('رأس مال ', doc_number), trader_fund_id),
            (voucher_id, NEW.document_date, 0.00, NEW.amount, CONCAT('رأس مال ', doc_number), NEW.fund_type_id);
    END IF;
END
$$
DELIMITER ;
DELIMITER $$
CREATE TRIGGER `after_financial_doc_update` AFTER UPDATE ON `financial_documents` FOR EACH ROW BEGIN
    -- حذف القيود القديمة
    DELETE va FROM voucher_account va
    INNER JOIN voucher v ON va.voucher_id = v.id
    WHERE v.no = OLD.id AND v.type_id = 1;
    
    DELETE FROM voucher WHERE no = OLD.id AND type_id = 1;
    
    -- إعادة إنشاء القيود الجديدة إذا لم يكن محذوف
    IF NEW.deleted_at IS NULL THEN
        CALL create_financial_entry(NEW.id, NEW.fund_type_id, NEW.document_type, 
                                   NEW.document_date, NEW.amount, NEW.trader_id);
    END IF;
END
$$
DELIMITER ;

-- --------------------------------------------------------

--
-- Table structure for table `financial_funds`
--

CREATE TABLE `financial_funds` (
  `id` int(11) NOT NULL,
  `name` varchar(200) NOT NULL,
  `code` varchar(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `financial_funds`
--

INSERT INTO `financial_funds` (`id`, `name`, `code`) VALUES
(1, 'صندوق الخزنة', NULL),
(2, 'صندوق المحاسب', NULL),
(3, 'صندوق المدير', NULL),
(4, 'reda', 'acc124'),
(5, 'scsd', 'ccx11231');

-- --------------------------------------------------------

--
-- Table structure for table `fines`
--

CREATE TABLE `fines` (
  `id` int(11) NOT NULL,
  `trader_id` int(11) NOT NULL,
  `container_id` int(11) NOT NULL,
  `fine_type` enum('delay','damages','other','') NOT NULL,
  `amount` int(11) NOT NULL,
  `fine_date` date NOT NULL,
  `notes` text NOT NULL,
  `created_by` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `deleted_by` int(11) DEFAULT NULL,
  `deleted_at` date DEFAULT NULL,
  `updated_by` int(11) DEFAULT NULL,
  `updated_at` date DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `fines`
--

INSERT INTO `fines` (`id`, `trader_id`, `container_id`, `fine_type`, `amount`, `fine_date`, `notes`, `created_by`, `created_at`, `deleted_by`, `deleted_at`, `updated_by`, `updated_at`) VALUES
(1, 2, 2, 'delay', 5000, '2025-08-05', 'mbv', -1, '2025-08-10 20:06:18', NULL, NULL, NULL, NULL),
(2, 2, 2, 'damages', 5000000, '2025-08-06', 'fhfghf', -1, '2025-08-17 19:56:00', NULL, NULL, NULL, NULL),
(3, 3, 3, 'damages', 500000, '2025-08-20', 'يبلبي', -1, '2025-08-18 07:27:49', NULL, NULL, NULL, NULL);

--
-- Triggers `fines`
--
DELIMITER $$
CREATE TRIGGER `after_fines_insert` AFTER INSERT ON `fines` FOR EACH ROW BEGIN
    DECLARE voucher_id INT;
    DECLARE trader_fund_id INT;
    DECLARE fines_account_id INT DEFAULT 1;
    DECLARE doc_type_code VARCHAR(10);
    DECLARE doc_number VARCHAR(20);
    
    -- تحديد كود نوع الغرامة
    SET doc_type_code = CASE NEW.fine_type
        WHEN 'delay' THEN 'FINE_DELAY'
        WHEN 'damages' THEN 'FINE_DAMAGE'
        ELSE 'FINE_OTHER'
    END;
    
    -- توليد رقم الغرامة
    SET doc_number = get_next_document_number(doc_type_code);
    
    -- الحصول على معرف حساب التاجر
    SELECT f.id INTO trader_fund_id 
    FROM financial_funds f
    INNER JOIN traders t ON f.code = t.code
    WHERE t.id = NEW.trader_id;
    
    -- إنشاء قسيمة جديدة
    INSERT INTO voucher (no, document_number, document_type_code, type_id, date)
    VALUES (NEW.id, doc_number, doc_type_code, 2, NEW.fine_date);
    
    SET voucher_id = LAST_INSERT_ID();
    
    -- القيد المحاسبي للغرامة
    INSERT INTO voucher_account (voucher_id, date, depit, credit, note, account_id)
    VALUES 
        (voucher_id, NEW.fine_date, NEW.amount, 0, 
         CONCAT('غرامة ', NEW.fine_type, ' ', doc_number, ' - حاوية ', NEW.container_id), trader_fund_id),
        (voucher_id, NEW.fine_date, 0, NEW.amount, 
         CONCAT('إيراد غرامة ', doc_number), fines_account_id);
END
$$
DELIMITER ;

-- --------------------------------------------------------

--
-- Stand-in structure for view `general_ledger`
-- (See below for the actual view)
--
CREATE TABLE `general_ledger` (
`voucher_id` int(11)
,`voucher_number` varchar(11)
,`type_id` int(11)
,`voucher_type_name` varchar(10)
,`voucher_date` date
,`entry_id` int(11)
,`account_id` int(11)
,`account_name` varchar(200)
,`account_code` varchar(11)
,`depit` double(11,2)
,`credit` double(11,2)
,`note` text
,`entry_date` date
);

-- --------------------------------------------------------

--
-- Table structure for table `other_fees`
--

CREATE TABLE `other_fees` (
  `id` int(11) NOT NULL,
  `trader_id` int(11) NOT NULL,
  `container_id` int(11) NOT NULL,
  `types_fee` enum('returns and fees','location fee','handling fee','other') NOT NULL,
  `amount` int(11) NOT NULL,
  `fee_date` date NOT NULL,
  `notes` text NOT NULL,
  `created_by` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `deleted_by` int(11) DEFAULT NULL,
  `deleted_at` date DEFAULT NULL,
  `updated_by` int(11) DEFAULT NULL,
  `updated_at` date DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `other_fees`
--

INSERT INTO `other_fees` (`id`, `trader_id`, `container_id`, `types_fee`, `amount`, `fee_date`, `notes`, `created_by`, `created_at`, `deleted_by`, `deleted_at`, `updated_by`, `updated_at`) VALUES
(1, 3, 3, 'returns and fees', 500000, '2025-08-07', 'nhjbj', -1, '2025-08-18 07:41:12', NULL, NULL, NULL, NULL);

--
-- Triggers `other_fees`
--
DELIMITER $$
CREATE TRIGGER `after_other_fees_insert` AFTER INSERT ON `other_fees` FOR EACH ROW BEGIN
    DECLARE voucher_id INT;
    DECLARE trader_fund_id INT;
    DECLARE fees_account_id INT DEFAULT 1;
    DECLARE doc_type_code VARCHAR(10);
    DECLARE doc_number VARCHAR(20);
    
    -- تحديد كود نوع الرسوم
    SET doc_type_code = CASE NEW.types_fee
        WHEN 'location fee' THEN 'FEE_LOCATION'
        WHEN 'handling fee' THEN 'FEE_HANDLING'
        WHEN 'returns and fees' THEN 'FEE_RETURN'
        ELSE 'FEE_OTHER'
    END;
    
    -- توليد رقم الرسوم
    SET doc_number = get_next_document_number(doc_type_code);
    
    -- الحصول على معرف حساب التاجر
    SELECT f.id INTO trader_fund_id 
    FROM financial_funds f
    INNER JOIN traders t ON f.code = t.code
    WHERE t.id = NEW.trader_id;
    
    -- إنشاء قسيمة جديدة
    INSERT INTO voucher (no, document_number, document_type_code, type_id, date)
    VALUES (NEW.id, doc_number, doc_type_code, 3, NEW.fee_date);
    
    SET voucher_id = LAST_INSERT_ID();
    
    -- القيد المحاسبي للرسوم
    INSERT INTO voucher_account (voucher_id, date, depit, credit, note, account_id)
    VALUES 
        (voucher_id, NEW.fee_date, NEW.amount, 0, 
         CONCAT('رسوم ', NEW.types_fee, ' ', doc_number, ' - حاوية ', NEW.container_id), trader_fund_id),
        (voucher_id, NEW.fee_date, 0, NEW.amount, 
         CONCAT('إيراد رسوم ', doc_number), fees_account_id);
END
$$
DELIMITER ;

-- --------------------------------------------------------

--
-- Stand-in structure for view `payment doc`
-- (See below for the actual view)
--
CREATE TABLE `payment doc` (
`id` int(11)
,`fund_type_id` int(11)
,`document_type` enum('receipt','payment','expense','transfer','capital')
,`document_date` date
,`amount` int(11)
,`trader_id` int(11)
,`notes` text
,`created_by` int(11)
,`created_at` timestamp
,`deleted_at` datetime
,`deleted_by` int(11)
,`updated_at` datetime
,`updated_by` int(11)
);

-- --------------------------------------------------------

--
-- Stand-in structure for view `sequence_status`
-- (See below for the actual view)
--
CREATE TABLE `sequence_status` (
`نوع المستند` varchar(50)
,`الكود` varchar(10)
,`البادئة` varchar(10)
,`التسلسل الحالي` int(11)
,`الرقم التالي` longtext
,`نشط` tinyint(1)
);

-- --------------------------------------------------------

--
-- Table structure for table `subscriptions`
--

CREATE TABLE `subscriptions` (
  `Id` int(11) NOT NULL,
  `User` varchar(255) DEFAULT NULL,
  `Endpoint` longtext NOT NULL,
  `PublicKey` varchar(255) NOT NULL,
  `AuthenticationToken` varchar(255) NOT NULL,
  `ContentEncoding` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `traders`
--

CREATE TABLE `traders` (
  `id` int(11) NOT NULL,
  `code` varchar(11) NOT NULL COMMENT 'رقم الحساب',
  `name` varchar(255) NOT NULL,
  `phone` varchar(50) NOT NULL,
  `email` varchar(50) NOT NULL,
  `address` varchar(255) NOT NULL,
  `notes` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `created_by` int(11) NOT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `deleted_by` int(11) DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `updated_by` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `traders`
--

INSERT INTO `traders` (`id`, `code`, `name`, `phone`, `email`, `address`, `notes`, `created_at`, `created_by`, `deleted_at`, `deleted_by`, `updated_at`, `updated_by`) VALUES
(1, 'count', 'عبدالرضا طاهر حسين', '07803883850', '<EMAIL>', 'العراق , البصرة , شارع الجزائر565', NULL, '2025-07-29 21:00:00', -1, NULL, NULL, NULL, NULL),
(2, 'acc123', 'علي', '354354', '<EMAIL>', 'shat alarab', NULL, '2025-07-30 21:00:00', -1, NULL, NULL, '2025-08-06 05:27:56', -1),
(3, 'acc124', 'reda', '*********', '<EMAIL>', 'dfsfsdfsd', 'sdfsd', '2025-08-05 21:00:00', -1, NULL, NULL, NULL, NULL),
(4, 'ccx11231', 'scsd', '3453453', '<EMAIL>', 'dfgfdg', NULL, '2025-08-09 21:00:00', -1, NULL, NULL, NULL, NULL);

--
-- Triggers `traders`
--
DELIMITER $$
CREATE TRIGGER `after_trader_insert` AFTER INSERT ON `traders` FOR EACH ROW BEGIN
    INSERT INTO financial_funds (name, code)
    VALUES (NEW.name, NEW.code);
END
$$
DELIMITER ;
DELIMITER $$
CREATE TRIGGER `after_trader_update` AFTER UPDATE ON `traders` FOR EACH ROW BEGIN
    -- تحديث البيانات في جدول financial_funds بناءً على الـ code القديم
    UPDATE financial_funds 
    SET name = NEW.name, code = NEW.code
    WHERE code = OLD.code;
END
$$
DELIMITER ;

-- --------------------------------------------------------

--
-- Stand-in structure for view `trader_account_balances`
-- (See below for the actual view)
--
CREATE TABLE `trader_account_balances` (
`trader_id` int(11)
,`trader_name` varchar(255)
,`account_code` varchar(11)
,`phone` varchar(50)
,`email` varchar(50)
,`address` varchar(255)
,`total_debit` double(19,2)
,`total_credit` double(19,2)
,`net_balance` double(19,2)
,`balance_type` varchar(6)
,`debtor_amount` double(19,2)
,`creditor_amount` double(19,2)
,`last_transaction_date` date
,`total_entries` bigint(21)
);

-- --------------------------------------------------------

--
-- Stand-in structure for view `trader_balances`
-- (See below for the actual view)
--
CREATE TABLE `trader_balances` (
`trader_id` int(11)
,`trader_name` varchar(255)
,`phone` varchar(50)
,`email` varchar(50)
,`address` varchar(255)
,`notes` text
,`total_receipts` decimal(32,0)
,`total_payments` decimal(32,0)
,`net_balance` decimal(33,0)
,`creditor_amount` decimal(33,0)
,`debtor_amount` decimal(33,0)
,`balance_type` varchar(6)
,`last_transaction_date` date
,`total_transactions` bigint(21)
);

-- --------------------------------------------------------

--
-- Stand-in structure for view `trader_balances2`
-- (See below for the actual view)
--
CREATE TABLE `trader_balances2` (
`trader_id` int(11)
,`trader_name` varchar(255)
,`phone` varchar(50)
,`email` varchar(50)
,`address` varchar(255)
,`notes` text
,`total_receipts` decimal(32,0)
,`total_payments` decimal(32,0)
,`net_balance` decimal(33,0)
,`creditor_amount` decimal(33,0)
,`debtor_amount` decimal(33,0)
,`balance_type` varchar(6)
,`last_transaction_date` date
,`total_transactions` bigint(21)
);

-- --------------------------------------------------------

--
-- Stand-in structure for view `trader_balances_view`
-- (See below for the actual view)
--
CREATE TABLE `trader_balances_view` (
`trader_id` int(11)
,`trader_code` varchar(11)
,`trader_name` varchar(255)
,`phone` varchar(50)
,`email` varchar(50)
,`address` varchar(255)
,`fund_id` int(11)
,`fund_name` varchar(200)
,`fund_code` varchar(11)
,`total_debit` double(19,2)
,`total_credit` double(19,2)
,`balance` double(19,2)
);

-- --------------------------------------------------------

--
-- Table structure for table `userlevelpermissions`
--

CREATE TABLE `userlevelpermissions` (
  `UserLevelID` int(11) NOT NULL,
  `TableName` varchar(255) NOT NULL,
  `Permission` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `userlevelpermissions`
--

INSERT INTO `userlevelpermissions` (`UserLevelID`, `TableName`, `Permission`) VALUES
(-2, '{0451B677-5316-4702-8E3E-9F2A4AD8EC4B}audittrail', 0),
(-2, '{0451B677-5316-4702-8E3E-9F2A4AD8EC4B}companies', 0),
(-2, '{0451B677-5316-4702-8E3E-9F2A4AD8EC4B}containers', 0),
(-2, '{0451B677-5316-4702-8E3E-9F2A4AD8EC4B}traders', 0),
(-2, '{0451B677-5316-4702-8E3E-9F2A4AD8EC4B}userlevelpermissions', 0),
(-2, '{0451B677-5316-4702-8E3E-9F2A4AD8EC4B}userlevels', 0),
(-2, '{0451B677-5316-4702-8E3E-9F2A4AD8EC4B}users', 0),
(0, '{0451B677-5316-4702-8E3E-9F2A4AD8EC4B}audittrail', 0),
(0, '{0451B677-5316-4702-8E3E-9F2A4AD8EC4B}companies', 0),
(0, '{0451B677-5316-4702-8E3E-9F2A4AD8EC4B}containers', 0),
(0, '{0451B677-5316-4702-8E3E-9F2A4AD8EC4B}traders', 0),
(0, '{0451B677-5316-4702-8E3E-9F2A4AD8EC4B}userlevelpermissions', 0),
(0, '{0451B677-5316-4702-8E3E-9F2A4AD8EC4B}userlevels', 0),
(0, '{0451B677-5316-4702-8E3E-9F2A4AD8EC4B}users', 0);

-- --------------------------------------------------------

--
-- Table structure for table `userlevels`
--

CREATE TABLE `userlevels` (
  `ID` int(11) NOT NULL,
  `Name` varchar(255) NOT NULL,
  `Hierarchy` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `userlevels`
--

INSERT INTO `userlevels` (`ID`, `Name`, `Hierarchy`) VALUES
(-2, 'Anonymous', NULL),
(-1, 'Administrator', NULL),
(0, 'Default', '');

-- --------------------------------------------------------

--
-- Table structure for table `users`
--

CREATE TABLE `users` (
  `id` int(11) NOT NULL,
  `full_name` varchar(150) NOT NULL,
  `phone` varchar(50) NOT NULL,
  `username` varchar(255) NOT NULL,
  `password` varchar(255) NOT NULL,
  `email` varchar(255) DEFAULT NULL,
  `photo` varchar(255) DEFAULT NULL,
  `userLevel` int(11) DEFAULT NULL,
  `activated` tinyint(1) DEFAULT 0,
  `user_type_id` int(11) DEFAULT NULL,
  `profile` longtext DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

--
-- Dumping data for table `users`
--

INSERT INTO `users` (`id`, `full_name`, `phone`, `username`, `password`, `email`, `photo`, `userLevel`, `activated`, `user_type_id`, `profile`, `created_at`) VALUES
(1, 'عبدالرضا طاهر حسين', '07803883850', 'reda', '123123', '<EMAIL>', NULL, -1, 0, NULL, NULL, '2025-07-30 00:00:00');

-- --------------------------------------------------------

--
-- Stand-in structure for view `view2`
-- (See below for the actual view)
--
CREATE TABLE `view2` (
`id` int(11)
,`fund_type_id` int(11)
,`document_type` enum('receipt','payment','expense','transfer','capital')
,`document_date` date
,`amount` int(11)
,`trader_id` int(11)
,`notes` text
,`created_by` int(11)
,`created_at` timestamp
,`deleted_at` datetime
,`deleted_by` int(11)
,`updated_at` datetime
,`updated_by` int(11)
);

-- --------------------------------------------------------

--
-- Table structure for table `voucher`
--

CREATE TABLE `voucher` (
  `id` int(11) NOT NULL,
  `no` varchar(11) NOT NULL COMMENT 'رقم المستند',
  `document_number` varchar(20) DEFAULT NULL,
  `document_type_code` varchar(10) DEFAULT NULL,
  `type_id` int(11) NOT NULL COMMENT 'نوع المستند',
  `date` date NOT NULL COMMENT 'تاريخ المستند'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `voucher`
--

INSERT INTO `voucher` (`id`, `no`, `document_number`, `document_type_code`, `type_id`, `date`) VALUES
(7, '16', 'PAY-2025-000003', 'PAYMENT', 1, '2025-08-09'),
(8, '17', 'REC-2025-000002', 'RECEIPT', 1, '2025-08-10'),
(9, '18', 'PAY-2025-000004', 'PAYMENT', 1, '2025-08-10'),
(10, '1', 'FDL-2025-000002', 'FINE_DELAY', 2, '2025-08-05'),
(11, '19', 'REC-2025-000003', 'RECEIPT', 1, '2025-08-15'),
(12, '20', 'PAY-2025-000005', 'PAYMENT', 1, '2025-08-15'),
(13, '21', 'EXP-2025-000004', 'EXPENSE', 1, '2025-08-15'),
(14, '2', 'FDM-2025-000002', 'FINE_DAMAG', 2, '2025-08-06'),
(15, '22', 'REC-2025-000004', 'RECEIPT', 1, '2025-08-18'),
(16, '23', 'PAY-2025-000006', 'PAYMENT', 1, '2025-08-18'),
(17, '3', 'FDM-2025-000003', 'FINE_DAMAG', 2, '2025-08-20'),
(18, '1', 'FRE-2025-000002', 'FEE_RETURN', 3, '2025-08-07');

-- --------------------------------------------------------

--
-- Table structure for table `voucher_account`
--

CREATE TABLE `voucher_account` (
  `id` int(11) NOT NULL,
  `voucher_id` int(11) NOT NULL,
  `date` date NOT NULL,
  `depit` double(11,2) NOT NULL COMMENT 'مدين',
  `credit` double(11,2) NOT NULL COMMENT 'دائن',
  `note` text DEFAULT NULL,
  `account_id` int(11) NOT NULL COMMENT 'رقم الحساب'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `voucher_account`
--

INSERT INTO `voucher_account` (`id`, `voucher_id`, `date`, `depit`, `credit`, `note`, `account_id`) VALUES
(19, 11, '2025-08-15', 500000.00, 0.00, 'إيصال استلام REC-2025-000003', 3),
(20, 11, '2025-08-15', 0.00, 500000.00, 'من العميل - REC-2025-000003', 3),
(21, 12, '2025-08-15', 0.00, 400000.00, 'إيصال دفع PAY-2025-000005', 3),
(22, 12, '2025-08-15', 400000.00, 0.00, 'للعميل - PAY-2025-000005', 3),
(23, 13, '2025-08-15', 700000.00, 0.00, 'مصروف EXP-2025-000004', 3),
(24, 13, '2025-08-15', 0.00, 700000.00, 'مصروف - EXP-2025-000004', 3),
(25, 14, '2025-08-06', 5000000.00, 0.00, 'غرامة damages FDM-2025-000002 - حاوية 2', 0),
(26, 14, '2025-08-06', 0.00, 5000000.00, 'إيراد غرامة FDM-2025-000002', 1),
(27, 15, '2025-08-18', 500000.00, 0.00, 'إيصال استلام REC-2025-000004', 2),
(28, 15, '2025-08-18', 0.00, 500000.00, 'من العميل - REC-2025-000004', 2),
(29, 16, '2025-08-18', 0.00, 30000.00, 'إيصال دفع PAY-2025-000006', 3),
(30, 16, '2025-08-18', 30000.00, 0.00, 'للعميل - PAY-2025-000006', 4),
(31, 17, '2025-08-20', 500000.00, 0.00, 'غرامة damages FDM-2025-000003 - حاوية 3', 4),
(32, 17, '2025-08-20', 0.00, 500000.00, 'إيراد غرامة FDM-2025-000003', 1),
(33, 18, '2025-08-07', 500000.00, 0.00, 'رسوم returns and fees FRE-2025-000002 - حاوية 3', 4),
(34, 18, '2025-08-07', 0.00, 500000.00, 'إيراد رسوم FRE-2025-000002', 1);

-- --------------------------------------------------------

--
-- Structure for view `customer_balances`
--
DROP TABLE IF EXISTS `customer_balances`;

CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `customer_balances`  AS SELECT `t`.`id` AS `customer_id`, `t`.`name` AS `customer_name`, `t`.`phone` AS `phone`, `t`.`email` AS `email`, `t`.`address` AS `address`, coalesce(sum(case when `fd`.`document_type` = 'receipt' then `fd`.`amount` when `fd`.`document_type` = 'transfer' and `fd`.`id` = `t`.`id` then `fd`.`amount` else 0 end),0) AS `total_receipts`, coalesce(sum(case when `fd`.`document_type` = 'payment' then `fd`.`amount` when `fd`.`document_type` = 'expense' then `fd`.`amount` else 0 end),0) AS `total_payments`, coalesce(sum(case when `fd`.`document_type` = 'receipt' then `fd`.`amount` when `fd`.`document_type` = 'transfer' and `fd`.`id` = `t`.`id` then `fd`.`amount` else 0 end),0) - coalesce(sum(case when `fd`.`document_type` = 'payment' then `fd`.`amount` when `fd`.`document_type` = 'expense' then `fd`.`amount` else 0 end),0) AS `balance`, max(`fd`.`document_date`) AS `last_transaction_date`, count(`fd`.`id`) AS `total_transactions` FROM (`traders` `t` left join `financial_documents` `fd` on(`t`.`id` = `fd`.`id`)) GROUP BY `t`.`id`, `t`.`name`, `t`.`phone`, `t`.`email`, `t`.`address` ORDER BY `t`.`name` ASC ;

-- --------------------------------------------------------

--
-- Structure for view `documents_with_numbers`
--
DROP TABLE IF EXISTS `documents_with_numbers`;

CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `documents_with_numbers`  AS SELECT `v`.`id` AS `voucher_id`, `v`.`no` AS `original_id`, `v`.`document_number` AS `document_number`, `v`.`document_type_code` AS `document_type_code`, `dt`.`type_name` AS `document_type_name`, `v`.`date` AS `document_date`, coalesce(sum(`va`.`depit`),0) AS `total_debit`, coalesce(sum(`va`.`credit`),0) AS `total_credit`, group_concat(distinct `ff`.`name` separator ', ') AS `involved_accounts` FROM (((`voucher` `v` left join `document_types` `dt` on(`v`.`document_type_code` = `dt`.`type_code`)) left join `voucher_account` `va` on(`v`.`id` = `va`.`voucher_id`)) left join `financial_funds` `ff` on(`va`.`account_id` = `ff`.`id`)) GROUP BY `v`.`id`, `v`.`no`, `v`.`document_number`, `v`.`document_type_code`, `dt`.`type_name`, `v`.`date` ORDER BY `v`.`date` DESC, `v`.`id` DESC ;

-- --------------------------------------------------------

--
-- Structure for view `general_ledger`
--
DROP TABLE IF EXISTS `general_ledger`;

CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `general_ledger`  AS SELECT `v`.`id` AS `voucher_id`, `v`.`no` AS `voucher_number`, `v`.`type_id` AS `type_id`, CASE `v`.`type_id` WHEN 1 THEN 'مستند مالي' WHEN 2 THEN 'غرامة' WHEN 3 THEN 'رسوم أخرى' ELSE 'غير محدد' END AS `voucher_type_name`, `v`.`date` AS `voucher_date`, `va`.`id` AS `entry_id`, `va`.`account_id` AS `account_id`, `ff`.`name` AS `account_name`, `ff`.`code` AS `account_code`, `va`.`depit` AS `depit`, `va`.`credit` AS `credit`, `va`.`note` AS `note`, `va`.`date` AS `entry_date` FROM ((`voucher` `v` join `voucher_account` `va` on(`v`.`id` = `va`.`voucher_id`)) left join `financial_funds` `ff` on(`va`.`account_id` = `ff`.`id`)) ORDER BY `v`.`date` DESC, `v`.`id` ASC, `va`.`id` ASC ;

-- --------------------------------------------------------

--
-- Structure for view `payment doc`
--
DROP TABLE IF EXISTS `payment doc`;

CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `payment doc`  AS SELECT `financial_documents`.`id` AS `id`, `financial_documents`.`fund_type_id` AS `fund_type_id`, `financial_documents`.`document_type` AS `document_type`, `financial_documents`.`document_date` AS `document_date`, `financial_documents`.`amount` AS `amount`, `financial_documents`.`trader_id` AS `trader_id`, `financial_documents`.`notes` AS `notes`, `financial_documents`.`created_by` AS `created_by`, `financial_documents`.`created_at` AS `created_at`, `financial_documents`.`deleted_at` AS `deleted_at`, `financial_documents`.`deleted_by` AS `deleted_by`, `financial_documents`.`updated_at` AS `updated_at`, `financial_documents`.`updated_by` AS `updated_by` FROM `financial_documents` WHERE `financial_documents`.`document_type` = 'payment' ;

-- --------------------------------------------------------

--
-- Structure for view `sequence_status`
--
DROP TABLE IF EXISTS `sequence_status`;

CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `sequence_status`  AS SELECT `document_types`.`type_name` AS `نوع المستند`, `document_types`.`type_code` AS `الكود`, `document_types`.`prefix` AS `البادئة`, `document_types`.`current_sequence` AS `التسلسل الحالي`, concat(`document_types`.`prefix`,'-',year(curdate()),'-',lpad(`document_types`.`current_sequence` + 1,`document_types`.`sequence_length`,'0')) AS `الرقم التالي`, `document_types`.`is_active` AS `نشط` FROM `document_types` ORDER BY `document_types`.`type_name` ASC ;

-- --------------------------------------------------------

--
-- Structure for view `trader_account_balances`
--
DROP TABLE IF EXISTS `trader_account_balances`;

CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `trader_account_balances`  AS SELECT `t`.`id` AS `trader_id`, `t`.`name` AS `trader_name`, `t`.`code` AS `account_code`, `t`.`phone` AS `phone`, `t`.`email` AS `email`, `t`.`address` AS `address`, coalesce(sum(`va`.`depit`),0) AS `total_debit`, coalesce(sum(`va`.`credit`),0) AS `total_credit`, coalesce(sum(`va`.`depit`),0) - coalesce(sum(`va`.`credit`),0) AS `net_balance`, CASE WHEN coalesce(sum(`va`.`depit`),0) - coalesce(sum(`va`.`credit`),0) > 0 THEN 'مدين' WHEN coalesce(sum(`va`.`depit`),0) - coalesce(sum(`va`.`credit`),0) < 0 THEN 'دائن' ELSE 'متوازن' END AS `balance_type`, CASE WHEN coalesce(sum(`va`.`depit`),0) - coalesce(sum(`va`.`credit`),0) > 0 THEN coalesce(sum(`va`.`depit`),0) - coalesce(sum(`va`.`credit`),0) ELSE 0 END AS `debtor_amount`, CASE WHEN coalesce(sum(`va`.`depit`),0) - coalesce(sum(`va`.`credit`),0) < 0 THEN abs(coalesce(sum(`va`.`depit`),0) - coalesce(sum(`va`.`credit`),0)) ELSE 0 END AS `creditor_amount`, max(`va`.`date`) AS `last_transaction_date`, count(`va`.`id`) AS `total_entries` FROM ((`traders` `t` left join `financial_funds` `ff` on(`t`.`code` = `ff`.`code`)) left join `voucher_account` `va` on(`ff`.`id` = `va`.`account_id`)) GROUP BY `t`.`id`, `t`.`name`, `t`.`code`, `t`.`phone`, `t`.`email`, `t`.`address` ORDER BY `t`.`name` ASC ;

-- --------------------------------------------------------

--
-- Structure for view `trader_balances`
--
DROP TABLE IF EXISTS `trader_balances`;

CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `trader_balances`  AS SELECT `t`.`id` AS `trader_id`, `t`.`name` AS `trader_name`, `t`.`phone` AS `phone`, `t`.`email` AS `email`, `t`.`address` AS `address`, `t`.`notes` AS `notes`, coalesce(sum(case when `fd`.`document_type` in ('receipt','transfer','capital') then `fd`.`amount` else 0 end),0) AS `total_receipts`, coalesce(sum(case when `fd`.`document_type` in ('payment','expense') then `fd`.`amount` else 0 end),0) AS `total_payments`, coalesce(sum(case when `fd`.`document_type` in ('receipt','transfer','capital') then `fd`.`amount` when `fd`.`document_type` in ('payment','expense') then -`fd`.`amount` else 0 end),0) AS `net_balance`, greatest(0,coalesce(sum(case when `fd`.`document_type` in ('receipt','transfer','capital') then `fd`.`amount` when `fd`.`document_type` in ('payment','expense') then -`fd`.`amount` else 0 end),0)) AS `creditor_amount`, greatest(0,-coalesce(sum(case when `fd`.`document_type` in ('receipt','transfer','capital') then `fd`.`amount` when `fd`.`document_type` in ('payment','expense') then -`fd`.`amount` else 0 end),0)) AS `debtor_amount`, CASE WHEN coalesce(sum(case when `fd`.`document_type` in ('receipt','transfer','capital') then `fd`.`amount` when `fd`.`document_type` in ('payment','expense') then -`fd`.`amount` else 0 end),0) > 0 THEN 'دائن' WHEN coalesce(sum(case when `fd`.`document_type` in ('receipt','transfer','capital') then `fd`.`amount` when `fd`.`document_type` in ('payment','expense') then -`fd`.`amount` else 0 end),0) < 0 THEN 'مدين' ELSE 'متوازن' END AS `balance_type`, max(`fd`.`document_date`) AS `last_transaction_date`, count(`fd`.`id`) AS `total_transactions` FROM (`traders` `t` left join `financial_documents` `fd` on(`t`.`id` = `fd`.`trader_id`)) GROUP BY `t`.`id`, `t`.`name`, `t`.`phone`, `t`.`email`, `t`.`address`, `t`.`notes` ORDER BY `t`.`name` ASC ;

-- --------------------------------------------------------

--
-- Structure for view `trader_balances2`
--
DROP TABLE IF EXISTS `trader_balances2`;

CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `trader_balances2`  AS SELECT `t`.`id` AS `trader_id`, `t`.`name` AS `trader_name`, `t`.`phone` AS `phone`, `t`.`email` AS `email`, `t`.`address` AS `address`, `t`.`notes` AS `notes`, coalesce(sum(case when `fd`.`document_type` = 'receipt' then `fd`.`amount` when `fd`.`document_type` = 'transfer' and `fd`.`trader_id` = `t`.`id` then `fd`.`amount` else 0 end),0) AS `total_receipts`, coalesce(sum(case when `fd`.`document_type` = 'payment' then `fd`.`amount` when `fd`.`document_type` = 'expense' then `fd`.`amount` else 0 end),0) AS `total_payments`, coalesce(sum(case when `fd`.`document_type` = 'receipt' then `fd`.`amount` when `fd`.`document_type` = 'transfer' and `fd`.`trader_id` = `t`.`id` then `fd`.`amount` else 0 end),0) - coalesce(sum(case when `fd`.`document_type` = 'payment' then `fd`.`amount` when `fd`.`document_type` = 'expense' then `fd`.`amount` else 0 end),0) AS `net_balance`, CASE WHEN coalesce(sum(case when `fd`.`document_type` = 'receipt' then `fd`.`amount` when `fd`.`document_type` = 'transfer' AND `fd`.`trader_id` = `t`.`id` then `fd`.`amount` else 0 end),0) - coalesce(sum(case when `fd`.`document_type` = 'payment' then `fd`.`amount` when `fd`.`document_type` = 'expense' then `fd`.`amount` else 0 end),0) > 0 THEN coalesce(sum(case when `fd`.`document_type` = 'receipt' then `fd`.`amount` when `fd`.`document_type` = 'transfer' and `fd`.`trader_id` = `t`.`id` then `fd`.`amount` else 0 end),0) - coalesce(sum(case when `fd`.`document_type` = 'payment' then `fd`.`amount` when `fd`.`document_type` = 'expense' then `fd`.`amount` else 0 end),0) ELSE 0 END AS `creditor_amount`, CASE WHEN coalesce(sum(case when `fd`.`document_type` = 'receipt' then `fd`.`amount` when `fd`.`document_type` = 'transfer' AND `fd`.`trader_id` = `t`.`id` then `fd`.`amount` else 0 end),0) - coalesce(sum(case when `fd`.`document_type` = 'payment' then `fd`.`amount` when `fd`.`document_type` = 'expense' then `fd`.`amount` else 0 end),0) < 0 THEN abs(coalesce(sum(case when `fd`.`document_type` = 'receipt' then `fd`.`amount` when `fd`.`document_type` = 'transfer' and `fd`.`trader_id` = `t`.`id` then `fd`.`amount` else 0 end),0) - coalesce(sum(case when `fd`.`document_type` = 'payment' then `fd`.`amount` when `fd`.`document_type` = 'expense' then `fd`.`amount` else 0 end),0)) ELSE 0 END AS `debtor_amount`, CASE WHEN coalesce(sum(case when `fd`.`document_type` = 'receipt' then `fd`.`amount` when `fd`.`document_type` = 'transfer' AND `fd`.`trader_id` = `t`.`id` then `fd`.`amount` else 0 end),0) - coalesce(sum(case when `fd`.`document_type` = 'payment' then `fd`.`amount` when `fd`.`document_type` = 'expense' then `fd`.`amount` else 0 end),0) > 0 THEN 'دائن' WHEN coalesce(sum(case when `fd`.`document_type` = 'receipt' then `fd`.`amount` when `fd`.`document_type` = 'transfer' AND `fd`.`trader_id` = `t`.`id` then `fd`.`amount` else 0 end),0) - coalesce(sum(case when `fd`.`document_type` = 'payment' then `fd`.`amount` when `fd`.`document_type` = 'expense' then `fd`.`amount` else 0 end),0) < 0 THEN 'مدين' ELSE 'متوازن' END AS `balance_type`, max(`fd`.`document_date`) AS `last_transaction_date`, count(`fd`.`id`) AS `total_transactions` FROM (`traders` `t` left join `financial_documents` `fd` on(`t`.`id` = `fd`.`trader_id`)) GROUP BY `t`.`id`, `t`.`name`, `t`.`phone`, `t`.`email`, `t`.`address`, `t`.`notes` ORDER BY `t`.`name` ASC ;

-- --------------------------------------------------------

--
-- Structure for view `trader_balances_view`
--
DROP TABLE IF EXISTS `trader_balances_view`;

CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `trader_balances_view`  AS SELECT `t`.`id` AS `trader_id`, `t`.`code` AS `trader_code`, `t`.`name` AS `trader_name`, `t`.`phone` AS `phone`, `t`.`email` AS `email`, `t`.`address` AS `address`, `ff`.`id` AS `fund_id`, `ff`.`name` AS `fund_name`, `ff`.`code` AS `fund_code`, coalesce(sum(`va`.`depit`),0) AS `total_debit`, coalesce(sum(`va`.`credit`),0) AS `total_credit`, coalesce(sum(`va`.`depit`),0) - coalesce(sum(`va`.`credit`),0) AS `balance` FROM ((`traders` `t` join `financial_funds` `ff` on(`ff`.`code` = `t`.`code`)) left join `voucher_account` `va` on(`va`.`account_id` = `ff`.`id`)) GROUP BY `t`.`id`, `ff`.`id` ;

-- --------------------------------------------------------

--
-- Structure for view `view2`
--
DROP TABLE IF EXISTS `view2`;

CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `view2`  AS SELECT `financial_documents`.`id` AS `id`, `financial_documents`.`fund_type_id` AS `fund_type_id`, `financial_documents`.`document_type` AS `document_type`, `financial_documents`.`document_date` AS `document_date`, `financial_documents`.`amount` AS `amount`, `financial_documents`.`trader_id` AS `trader_id`, `financial_documents`.`notes` AS `notes`, `financial_documents`.`created_by` AS `created_by`, `financial_documents`.`created_at` AS `created_at`, `financial_documents`.`deleted_at` AS `deleted_at`, `financial_documents`.`deleted_by` AS `deleted_by`, `financial_documents`.`updated_at` AS `updated_at`, `financial_documents`.`updated_by` AS `updated_by` FROM `financial_documents` WHERE `financial_documents`.`document_type` = 'receipt' ;

--
-- Indexes for dumped tables
--

--
-- Indexes for table `audittrail`
--
ALTER TABLE `audittrail`
  ADD PRIMARY KEY (`Id`);

--
-- Indexes for table `companies`
--
ALTER TABLE `companies`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `containers`
--
ALTER TABLE `containers`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `document_types`
--
ALTER TABLE `document_types`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `type_code` (`type_code`);

--
-- Indexes for table `exportlog`
--
ALTER TABLE `exportlog`
  ADD PRIMARY KEY (`FileId`);

--
-- Indexes for table `financial_documents`
--
ALTER TABLE `financial_documents`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `financial_funds`
--
ALTER TABLE `financial_funds`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `code` (`code`);

--
-- Indexes for table `fines`
--
ALTER TABLE `fines`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `other_fees`
--
ALTER TABLE `other_fees`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `subscriptions`
--
ALTER TABLE `subscriptions`
  ADD PRIMARY KEY (`Id`);

--
-- Indexes for table `traders`
--
ALTER TABLE `traders`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `code` (`code`);

--
-- Indexes for table `userlevelpermissions`
--
ALTER TABLE `userlevelpermissions`
  ADD PRIMARY KEY (`UserLevelID`,`TableName`);

--
-- Indexes for table `userlevels`
--
ALTER TABLE `userlevels`
  ADD PRIMARY KEY (`ID`);

--
-- Indexes for table `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `full_name` (`full_name`),
  ADD UNIQUE KEY `username` (`username`);

--
-- Indexes for table `voucher`
--
ALTER TABLE `voucher`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `voucher_account`
--
ALTER TABLE `voucher_account`
  ADD PRIMARY KEY (`id`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `audittrail`
--
ALTER TABLE `audittrail`
  MODIFY `Id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=206;

--
-- AUTO_INCREMENT for table `companies`
--
ALTER TABLE `companies`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `containers`
--
ALTER TABLE `containers`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `document_types`
--
ALTER TABLE `document_types`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=14;

--
-- AUTO_INCREMENT for table `financial_documents`
--
ALTER TABLE `financial_documents`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=24;

--
-- AUTO_INCREMENT for table `financial_funds`
--
ALTER TABLE `financial_funds`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `fines`
--
ALTER TABLE `fines`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `other_fees`
--
ALTER TABLE `other_fees`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `subscriptions`
--
ALTER TABLE `subscriptions`
  MODIFY `Id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `traders`
--
ALTER TABLE `traders`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `users`
--
ALTER TABLE `users`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `voucher`
--
ALTER TABLE `voucher`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=19;

--
-- AUTO_INCREMENT for table `voucher_account`
--
ALTER TABLE `voucher_account`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=35;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
